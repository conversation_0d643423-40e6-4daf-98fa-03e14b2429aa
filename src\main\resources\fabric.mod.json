{"schemaVersion": 1, "id": "template-mod", "version": "${version}", "name": "Template <PERSON>", "description": "This is an example description! Tell everyone what your mod is about!", "authors": ["Me!"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/template-mod/icon.png", "environment": "*", "entrypoints": {"main": ["change.thisname.TemplateMod"], "client": ["change.thisname.TemplateModClient"]}, "mixins": ["template-mod.mixins.json", {"config": "template-mod.client.mixins.json", "environment": "client"}], "depends": {"fabricloader": ">=0.17.2", "minecraft": "~1.21.5", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}