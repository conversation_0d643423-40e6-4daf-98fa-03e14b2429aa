package change.thisname;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.hit.HitResult;
import org.lwjgl.glfw.GLFW;

import change.thisname.bot.PathfindingBot;
import change.thisname.pathfinding.PathfindingOptions;

public class TemplateModClient implements ClientModInitializer {
	private static PathfindingBot pathfindingBot;

	// Key bindings
	private static KeyBinding setGoalKey;
	private static KeyBinding toggleBotKey;
	private static KeyBinding stopBotKey;
	private static KeyBinding toggleParkourKey;
	private static KeyBinding emergencyStopKey;

	@Override
	public void onInitializeClient() {
		// Register key bindings
		setGoalKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.pathfinding-bot.set_goal",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_G,
			"category.pathfinding-bot"
		));

		toggleBotKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.pathfinding-bot.toggle",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_B,
			"category.pathfinding-bot"
		));

		stopBotKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.pathfinding-bot.stop",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_N,
			"category.pathfinding-bot"
		));

		toggleParkourKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.pathfinding-bot.toggle_parkour",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_P,
			"category.pathfinding-bot"
		));

		emergencyStopKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.pathfinding-bot.emergency_stop",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_X,
			"category.pathfinding-bot"
		));

		// Register client tick event
		ClientTickEvents.END_CLIENT_TICK.register(client -> {
			if (client.player != null && client.world != null) {
				// Initialize bot if not already done
				if (pathfindingBot == null) {
					pathfindingBot = new PathfindingBot(client);
				}

				// Tick the bot
				pathfindingBot.tick();

				// Handle key presses
				handleKeyPresses(client);
			}
		});

		// Register world render event for pathfinding visualization
		WorldRenderEvents.AFTER_TRANSLUCENT.register((context) -> {
			if (pathfindingBot != null) {
				pathfindingBot.getRenderer().render(
					context.matrixStack(),
					context.consumers(),
					context.camera().getPos().x,
					context.camera().getPos().y,
					context.camera().getPos().z
				);
			}
		});

		TemplateMod.LOGGER.info("Pathfinding Bot client initialized!");
	}

	private void handleKeyPresses(net.minecraft.client.MinecraftClient client) {
		if (pathfindingBot == null) return;

		// Set goal key
		if (setGoalKey.wasPressed()) {
			if (client.crosshairTarget != null && client.crosshairTarget.getType() == HitResult.Type.BLOCK) {
				Vec3d goalPos = client.crosshairTarget.getPos();
				pathfindingBot.setGoal(goalPos);

				if (client.player != null) {
					client.player.sendMessage(
						net.minecraft.text.Text.literal("§aGoal set to: " +
							String.format("%.1f, %.1f, %.1f", goalPos.x, goalPos.y, goalPos.z)),
						true
					);
				}
			}
		}

		// Toggle bot key
		if (toggleBotKey.wasPressed()) {
			boolean isEnabled = pathfindingBot.getCurrentState() != PathfindingBot.BotState.DISABLED;
			pathfindingBot.setEnabled(!isEnabled);

			if (client.player != null) {
				client.player.sendMessage(
					net.minecraft.text.Text.literal(isEnabled ? "§cPathfinding Bot disabled" : "§aPathfinding Bot enabled"),
					true
				);
			}
		}

		// Stop bot key
		if (stopBotKey.wasPressed()) {
			pathfindingBot.stop();

			if (client.player != null) {
				client.player.sendMessage(
					net.minecraft.text.Text.literal("§ePathfinding Bot stopped"),
					true
				);
			}
		}

		// Toggle parkour key
		if (toggleParkourKey.wasPressed()) {
			PathfindingOptions options = pathfindingBot.getPathfindingOptions();
			boolean parkourEnabled = options.isAllowParkour();
			options.setAllowParkour(!parkourEnabled);

			if (client.player != null) {
				client.player.sendMessage(
					net.minecraft.text.Text.literal(parkourEnabled ? "§cParkour disabled" : "§aParkour enabled"),
					true
				);
			}
		}

		// Emergency stop key
		if (emergencyStopKey.wasPressed()) {
			pathfindingBot.emergencyStop();

			if (client.player != null) {
				client.player.sendMessage(
					net.minecraft.text.Text.literal("§4EMERGENCY STOP ACTIVATED"),
					true
				);
			}
		}
	}

	public static PathfindingBot getPathfindingBot() {
		return pathfindingBot;
	}
}