package change.thisname.pathfinding;

import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import change.thisname.physics.PhysicsSimulator;
import change.thisname.physics.MovementValidator;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Core A* pathfinding algorithm implementation.
 * Calculates optimal paths considering physics and movement constraints.
 */
public class AStarPathfinder {
    private final World world;
    private final PhysicsSimulator physicsSimulator;
    private final MovementValidator movementValidator;
    
    // A* algorithm data structures
    private final PriorityQueue<PathNode> openSet;
    private final Set<PathNode> closedSet;
    private final Map<BlockPos, PathNode> allNodes;
    
    // Pathfinding parameters
    private static final int MAX_ITERATIONS = 10000;
    private static final double MAX_SEARCH_DISTANCE = 100.0;
    private static final double JUMP_HEIGHT = 1.25; // Minecraft jump height
    private static final double PLAYER_WIDTH = 0.6;
    private static final double PLAYER_HEIGHT = 1.8;
    
    public AStarPathfinder(World world, PhysicsSimulator physicsSimulator, MovementValidator movementValidator) {
        this.world = world;
        this.physicsSimulator = physicsSimulator;
        this.movementValidator = movementValidator;
        this.openSet = new PriorityQueue<>();
        this.closedSet = new HashSet<>();
        this.allNodes = new ConcurrentHashMap<>();
    }
    
    /**
     * Find path from start to goal using A* algorithm
     */
    public PathfindingResult findPath(Vec3d start, Vec3d goal) {
        return findPath(start, goal, new PathfindingOptions());
    }
    
    /**
     * Find path with custom options
     */
    public PathfindingResult findPath(Vec3d start, Vec3d goal, PathfindingOptions options) {
        long startTime = System.currentTimeMillis();
        
        // Clear previous search data
        openSet.clear();
        closedSet.clear();
        allNodes.clear();
        
        // Create start and goal nodes
        PathNode startNode = getOrCreateNode(start);
        PathNode goalNode = getOrCreateNode(goal);
        
        // Initialize start node
        startNode.setGCost(0);
        startNode.calculateHCost(goalNode);
        openSet.add(startNode);
        
        int iterations = 0;
        PathNode currentNode = null;
        
        while (!openSet.isEmpty() && iterations < MAX_ITERATIONS) {
            currentNode = openSet.poll();
            closedSet.add(currentNode);
            
            // Check if we reached the goal
            if (isGoalReached(currentNode, goalNode, options.getGoalTolerance())) {
                break;
            }
            
            // Explore neighbors
            List<PathNode> neighbors = getNeighbors(currentNode, goalNode, options);
            for (PathNode neighbor : neighbors) {
                if (closedSet.contains(neighbor)) {
                    continue;
                }
                
                // Calculate costs
                neighbor.calculateGCost(startNode);
                neighbor.calculateHCost(goalNode);
                
                // Check if this path to neighbor is better
                PathNode existingNode = allNodes.get(neighbor.getPosition());
                if (existingNode != null && existingNode.getGCost() <= neighbor.getGCost()) {
                    continue;
                }
                
                // Update neighbor
                neighbor.setParent(currentNode);
                if (!openSet.contains(neighbor)) {
                    openSet.add(neighbor);
                }
            }
            
            iterations++;
        }
        
        long endTime = System.currentTimeMillis();
        
        // Build result
        PathfindingResult result = new PathfindingResult();
        result.setSuccess(currentNode != null && isGoalReached(currentNode, goalNode, options.getGoalTolerance()));
        result.setPath(reconstructPath(currentNode));
        result.setExploredNodes(new ArrayList<>(closedSet));
        result.setIterations(iterations);
        result.setComputationTime(endTime - startTime);
        result.setStartPosition(start);
        result.setGoalPosition(goal);
        
        return result;
    }
    
    /**
     * Get or create a node for the given position
     */
    private PathNode getOrCreateNode(Vec3d position) {
        BlockPos blockPos = BlockPos.ofFloored(position);
        return allNodes.computeIfAbsent(blockPos, pos -> new PathNode(position));
    }
    
    /**
     * Check if goal is reached within tolerance
     */
    private boolean isGoalReached(PathNode current, PathNode goal, double tolerance) {
        return current.getPrecisePosition().distanceTo(goal.getPrecisePosition()) <= tolerance;
    }
    
    /**
     * Get valid neighbors for the current node
     */
    private List<PathNode> getNeighbors(PathNode current, PathNode goal, PathfindingOptions options) {
        List<PathNode> neighbors = new ArrayList<>();
        Vec3d currentPos = current.getPrecisePosition();
        
        // Basic movement directions (8-directional + vertical)
        int[][] directions = {
            {1, 0, 0}, {-1, 0, 0}, {0, 0, 1}, {0, 0, -1},  // Cardinal
            {1, 0, 1}, {1, 0, -1}, {-1, 0, 1}, {-1, 0, -1}, // Diagonal
            {0, 1, 0}, {0, -1, 0}  // Vertical
        };
        
        for (int[] dir : directions) {
            Vec3d newPos = currentPos.add(dir[0], dir[1], dir[2]);
            
            // Skip if too far from goal
            if (newPos.distanceTo(goal.getPrecisePosition()) > MAX_SEARCH_DISTANCE) {
                continue;
            }
            
            PathNode neighbor = getOrCreateNode(newPos);
            
            // Validate basic movement
            if (movementValidator.canMoveTo(currentPos, newPos)) {
                neighbor.setMovementType(PathNode.MovementType.WALK);
                neighbors.add(neighbor);
            }
            
            // Check for jump movements if enabled
            if (options.isAllowJumping() && dir[1] >= 0) {
                Vec3d jumpPos = newPos.add(0, JUMP_HEIGHT, 0);
                if (movementValidator.canJumpTo(currentPos, jumpPos)) {
                    PathNode jumpNeighbor = getOrCreateNode(jumpPos);
                    jumpNeighbor.setMovementType(PathNode.MovementType.JUMP);
                    jumpNeighbor.setJumpNode(true);
                    neighbors.add(jumpNeighbor);
                }
            }
            
            // Check for parkour movements if enabled
            if (options.isAllowParkour()) {
                List<PathNode> parkourNodes = generateParkourMoves(current, neighbor, goal);
                neighbors.addAll(parkourNodes);
            }
        }
        
        return neighbors;
    }
    
    /**
     * Generate parkour movement options
     */
    private List<PathNode> generateParkourMoves(PathNode current, PathNode target, PathNode goal) {
        List<PathNode> parkourMoves = new ArrayList<>();
        Vec3d currentPos = current.getPrecisePosition();
        Vec3d targetPos = target.getPrecisePosition();
        
        // Gap jumps (2-4 block distances)
        for (int distance = 2; distance <= 4; distance++) {
            Vec3d direction = targetPos.subtract(currentPos).normalize();
            Vec3d jumpTarget = currentPos.add(direction.multiply(distance));
            
            if (physicsSimulator.canPerformGapJump(currentPos, jumpTarget)) {
                PathNode parkourNode = getOrCreateNode(jumpTarget);
                parkourNode.setMovementType(PathNode.MovementType.PARKOUR_JUMP);
                parkourNode.setParkourNode(true);
                parkourNode.setJumpDirection(direction);
                parkourNode.setJumpVelocity(physicsSimulator.calculateRequiredJumpVelocity(currentPos, jumpTarget));
                parkourMoves.add(parkourNode);
            }
        }
        
        // Wall climbs (if there's a wall nearby)
        Vec3d upPos = currentPos.add(0, 2, 0);
        if (physicsSimulator.canClimbWall(currentPos, upPos)) {
            PathNode climbNode = getOrCreateNode(upPos);
            climbNode.setMovementType(PathNode.MovementType.CLIMB);
            climbNode.setParkourNode(true);
            parkourMoves.add(climbNode);
        }
        
        return parkourMoves;
    }
    
    /**
     * Reconstruct path from goal to start
     */
    private List<PathNode> reconstructPath(PathNode goalNode) {
        if (goalNode == null) {
            return new ArrayList<>();
        }
        
        List<PathNode> path = new ArrayList<>();
        PathNode current = goalNode;
        
        while (current != null) {
            path.add(0, current);
            current = current.getParent();
        }
        
        return path;
    }
}
