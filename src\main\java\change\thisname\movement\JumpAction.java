package change.thisname.movement;

import net.minecraft.util.math.Vec3d;
import change.thisname.physics.PhysicsSimulator;
import change.thisname.physics.JumpTrajectory;

/**
 * Jump movement action with physics simulation
 */
public class JumpAction extends MovementAction {
    private JumpTrajectory trajectory;
    private Vec3d currentPosition;
    private Vec3d currentVelocity;
    private int currentTick;
    
    public JumpAction(Vec3d startPosition, Vec3d targetPosition) {
        super(startPosition, targetPosition, ActionType.JUMP, 
              calculateJumpDuration(startPosition, targetPosition), 2.0);
        this.currentPosition = startPosition;
        this.currentVelocity = Vec3d.ZERO;
        this.currentTick = 0;
    }
    
    private static double calculateJumpDuration(Vec3d start, Vec3d target) {
        // Estimate jump duration based on distance and height
        double horizontalDistance = Math.sqrt(
            Math.pow(target.x - start.x, 2) + Math.pow(target.z - start.z, 2)
        );
        double verticalDistance = target.y - start.y;
        
        // Basic physics calculation for jump time
        double estimatedTicks = Math.max(10, horizontalDistance * 2 + Math.abs(verticalDistance) * 3);
        return estimatedTicks / 20.0; // Convert to seconds
    }
    
    @Override
    public boolean validatePhysics(PhysicsSimulator physicsSimulator) {
        // Simulate the jump trajectory
        trajectory = physicsSimulator.simulateJump(startPosition, targetPosition);
        
        if (!trajectory.isSuccess()) {
            setValidationError("Jump trajectory failed: cannot reach target");
            return false;
        }
        
        if (trajectory.hasCollision()) {
            setValidationError("Jump trajectory blocked by collision at " + trajectory.getCollisionPoint());
            return false;
        }
        
        // Check if landing point is safe
        Vec3d landingPoint = trajectory.getLandingPoint();
        if (landingPoint != null && !physicsSimulator.isPositionClear(landingPoint)) {
            setValidationError("Landing point is not safe");
            return false;
        }
        
        setPhysicsValidated(true);
        return true;
    }
    
    @Override
    public ActionResult executeTick(IMovementController controller) {
        if (!started) {
            start();
        }
        
        updateProgress();
        
        // Get position from trajectory
        if (trajectory != null && currentTick < trajectory.getTrajectoryPoints().size()) {
            JumpTrajectory.TrajectoryPoint point = trajectory.getTrajectoryPoints().get(currentTick);
            currentPosition = point.getPosition();
            currentVelocity = point.getVelocity();
        } else {
            // Fallback to interpolation if trajectory is not available
            currentPosition = interpolatePosition();
        }
        
        // Apply inputs
        InputState inputs = getRequiredInputs();
        controller.applyInputs(inputs);
        
        currentTick++;
        
        return new ActionResult(true, currentPosition, completed);
    }
    
    @Override
    public Vec3d getCurrentPosition() {
        return currentPosition;
    }
    
    @Override
    public InputState getRequiredInputs() {
        InputState inputs = new InputState();
        
        if (completed) {
            return inputs;
        }
        
        // Jump input is only needed at the beginning
        if (currentTick <= 1) {
            inputs.jump(true);
        }
        
        // Calculate horizontal movement direction
        Vec3d horizontalDirection = new Vec3d(
            targetPosition.x - startPosition.x,
            0,
            targetPosition.z - startPosition.z
        ).normalize();
        
        // Apply directional inputs throughout the jump
        if (Math.abs(horizontalDirection.z) > Math.abs(horizontalDirection.x)) {
            if (horizontalDirection.z > 0) {
                inputs.forward(true);
            } else {
                inputs.backward(true);
            }
        } else {
            if (horizontalDirection.x > 0) {
                inputs.right(true);
            } else {
                inputs.left(true);
            }
        }
        
        return inputs;
    }
    
    /**
     * Get the jump trajectory for rendering
     */
    public JumpTrajectory getTrajectory() {
        return trajectory;
    }
    
    /**
     * Get current velocity for physics calculations
     */
    public Vec3d getCurrentVelocity() {
        return currentVelocity;
    }
    
    /**
     * Check if jump is at peak height
     */
    public boolean isAtPeak() {
        return currentVelocity != null && Math.abs(currentVelocity.y) < 0.01;
    }
    
    /**
     * Check if jump is in falling phase
     */
    public boolean isFalling() {
        return currentVelocity != null && currentVelocity.y < -0.01;
    }
}
