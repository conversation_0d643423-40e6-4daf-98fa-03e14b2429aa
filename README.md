# Pathfinding Bot - Advanced A* Pathfinding for Minecraft

An advanced Minecraft Fabric mod that implements a sophisticated A* pathfinding bot with physics simulation, parkour capabilities, and comprehensive visualization.

## Features

### Core Pathfinding
- **A* Algorithm**: Implements proper A* pathfinding with G (actual cost) and H (heuristic) calculations
- **Asynchronous Processing**: Runs pathfinding calculations on separate threads to avoid blocking the main game thread
- **Dynamic Environment Handling**: Automatically recalculates paths when the environment changes

### Advanced Physics Simulation
- **Accurate Physics**: Replicates Minecraft's physics engine for movement validation
- **Jump Trajectory Calculation**: Simulates jump trajectories with gravity, drag, and collision detection
- **Movement Validation**: Validates all movements using physics simulation before execution

### Parkour Capabilities
- **Gap Jumps**: Calculates and executes jumps across gaps up to 4.5 blocks
- **Precision Jumps**: Performs precise landings on small platforms
- **Wall Climbing**: Climbs walls up to 3 blocks high
- **Momentum Jumps**: Long-distance jumps with momentum buildup
- **Physics-Based Validation**: All parkour moves are validated using accurate physics simulation

### Comprehensive Rendering
- **Path Visualization**: Renders the complete calculated path with different colors for movement types
- **Explored Nodes**: Shows all nodes explored during pathfinding
- **Goal Markers**: Pulsing goal destination markers
- **Jump Trajectories**: Visualizes jump arcs and landing points
- **Obstacle Detection**: Highlights detected obstacles and blocked areas

### Granular Movement System
- **Individual Actions**: Uses granular actions like "jump", "walk", "sprint" instead of simplified block-based commands
- **Physics Integration**: Each movement action is validated through physics simulation
- **Action Queuing**: Supports queuing multiple movement actions for complex sequences

## Controls

| Key | Action | Description |
|-----|--------|-------------|
| `G` | Set Goal | Sets the goal position to where you're looking |
| `B` | Toggle Bot | Enables/disables the pathfinding bot |
| `N` | Stop Bot | Stops current pathfinding and movement |
| `P` | Toggle Parkour | Enables/disables parkour movements |
| `X` | Emergency Stop | Immediately halts all movement and pathfinding |

## Usage

1. **Enable the Bot**: Press `B` to enable the pathfinding bot
2. **Set a Goal**: Look at your desired destination and press `G`
3. **Watch the Magic**: The bot will calculate a path and automatically navigate to the goal
4. **Advanced Movement**: Press `P` to enable parkour mode for more advanced movement capabilities

## Technical Architecture

### Core Components

- **PathfindingBot**: Main controller coordinating all systems
- **AStarPathfinder**: Core A* algorithm implementation
- **AsyncPathfindingManager**: Handles asynchronous pathfinding operations
- **PhysicsSimulator**: Accurate Minecraft physics simulation
- **MovementValidator**: Validates movement feasibility
- **MovementController**: Executes movement actions
- **ParkourCalculator**: Calculates advanced parkour movements
- **PathfindingRenderer**: Comprehensive visualization system

### Movement Actions

- **WalkAction**: Basic walking/sprinting movement
- **JumpAction**: Physics-simulated jumping
- **ParkourAction**: Advanced parkour movements with preparation phases

### Physics Features

- **Jump Trajectory Simulation**: Calculates complete jump arcs with gravity and drag
- **Collision Detection**: Accurate collision detection using player bounding boxes
- **Fall Damage Calculation**: Estimates fall damage for safety validation
- **Movement Time Estimation**: Calculates realistic movement times

## Configuration

The bot supports various pathfinding options:

```java
PathfindingOptions options = new PathfindingOptions()
    .allowJumping(true)
    .allowParkour(true)
    .allowSprinting(true)
    .maxJumpDistance(4.0)
    .goalTolerance(1.0)
    .preferSaferPaths(false);
```

## Building

This mod uses Fabric and requires:
- Java 21
- Minecraft 1.21.5
- Fabric Loader 0.17.2+
- Fabric API

To build:
```bash
./gradlew build
```

## Installation

1. Install Fabric Loader for Minecraft 1.21.5
2. Download Fabric API
3. Place the built mod JAR in your mods folder
4. Launch Minecraft with the Fabric profile

## Safety Features

- **Physics Validation**: All movements are validated before execution
- **Damage Avoidance**: Avoids movements that would cause fall damage
- **Emergency Stop**: Immediate halt capability for safety
- **Collision Prevention**: Prevents movements into solid blocks
- **Lava/Void Detection**: Avoids dangerous areas

## Performance

- **Asynchronous Processing**: Pathfinding runs on separate threads
- **Efficient Algorithms**: Optimized A* implementation with proper data structures
- **Limited Search Space**: Configurable search distance limits
- **Smart Caching**: Reuses calculations where possible

## Contributing

This mod demonstrates advanced pathfinding concepts and can be extended with:
- Additional movement types
- More sophisticated heuristics
- Machine learning integration
- Multi-goal pathfinding
- Collaborative pathfinding for multiple bots

## License

This project is licensed under CC0-1.0 - see the LICENSE file for details.
