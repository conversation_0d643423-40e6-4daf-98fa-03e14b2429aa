package change.thisname.movement;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.util.math.Vec3d;
import change.thisname.physics.PhysicsSimulator;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Controls player movement by executing movement actions
 */
public class MovementController {
    private final MinecraftClient client;
    private final PhysicsSimulator physicsSimulator;
    
    // Movement action queue
    private final Queue<MovementAction> actionQueue;
    private MovementAction currentAction;
    
    // Input state management
    private MovementAction.InputState currentInputs;
    private MovementAction.InputState previousInputs;
    
    // Movement state
    private Vec3d lastPosition;
    private boolean enabled = false;
    
    public MovementController(MinecraftClient client, PhysicsSimulator physicsSimulator) {
        this.client = client;
        this.physicsSimulator = physicsSimulator;
        this.actionQueue = new ConcurrentLinkedQueue<>();
        this.currentInputs = new MovementAction.InputState();
        this.previousInputs = new MovementAction.InputState();
    }
    
    /**
     * Add a movement action to the queue
     */
    public void queueAction(MovementAction action) {
        // Validate physics before queueing
        if (!action.validatePhysics(physicsSimulator)) {
            throw new IllegalArgumentException("Action failed physics validation: " + action.getValidationError());
        }
        
        actionQueue.offer(action);
    }
    
    /**
     * Execute queued actions for one tick
     */
    public void tick() {
        if (!enabled || client.player == null) {
            return;
        }
        
        // Update last position
        lastPosition = client.player.getPos();
        
        // Process current action
        if (currentAction == null || currentAction.isCompleted()) {
            // Get next action from queue
            currentAction = actionQueue.poll();
            if (currentAction == null) {
                // No actions to execute, clear inputs
                clearInputs();
                return;
            }
        }
        
        // Execute current action
        try {
            MovementAction.ActionResult result = currentAction.executeTick(this);
            
            if (!result.isSuccess()) {
                // Action failed, cancel and clear
                currentAction.cancel();
                currentAction = null;
                clearInputs();
                return;
            }
            
            if (result.isActionComplete()) {
                currentAction = null;
            }
            
        } catch (Exception e) {
            // Handle action execution error
            if (currentAction != null) {
                currentAction.cancel();
                currentAction = null;
            }
            clearInputs();
        }
    }
    
    /**
     * Apply input state to game controls
     */
    public void applyInputs(MovementAction.InputState inputs) {
        if (client.player == null) {
            return;
        }
        
        // Store previous inputs for comparison
        previousInputs = currentInputs;
        currentInputs = inputs;
        
        // Apply movement inputs
        setKeyState(client.options.forwardKey, inputs.isForward());
        setKeyState(client.options.backKey, inputs.isBackward());
        setKeyState(client.options.leftKey, inputs.isLeft());
        setKeyState(client.options.rightKey, inputs.isRight());
        setKeyState(client.options.jumpKey, inputs.isJump());
        setKeyState(client.options.sneakKey, inputs.isSneak());
        setKeyState(client.options.sprintKey, inputs.isSprint());
    }
    
    /**
     * Set key binding state
     */
    private void setKeyState(KeyBinding key, boolean pressed) {
        if (pressed) {
            if (!key.isPressed()) {
                key.setPressed(true);
            }
        } else {
            if (key.isPressed()) {
                key.setPressed(false);
            }
        }
    }
    
    /**
     * Clear all input states
     */
    public void clearInputs() {
        applyInputs(new MovementAction.InputState());
    }
    
    /**
     * Cancel current action and clear queue
     */
    public void cancelAll() {
        if (currentAction != null) {
            currentAction.cancel();
            currentAction = null;
        }
        
        actionQueue.clear();
        clearInputs();
    }
    
    /**
     * Cancel current action only
     */
    public void cancelCurrent() {
        if (currentAction != null && currentAction.canInterrupt()) {
            currentAction.cancel();
            currentAction = null;
            clearInputs();
        }
    }
    
    /**
     * Get current player position
     */
    public Vec3d getCurrentPosition() {
        if (client.player != null) {
            return client.player.getPos();
        }
        return lastPosition != null ? lastPosition : Vec3d.ZERO;
    }
    
    /**
     * Check if movement controller is active
     */
    public boolean isActive() {
        return enabled && (currentAction != null || !actionQueue.isEmpty());
    }
    
    /**
     * Get current action being executed
     */
    public MovementAction getCurrentAction() {
        return currentAction;
    }
    
    /**
     * Get number of queued actions
     */
    public int getQueueSize() {
        return actionQueue.size();
    }
    
    /**
     * Check if player has moved since last tick
     */
    public boolean hasPlayerMoved() {
        if (client.player == null || lastPosition == null) {
            return false;
        }
        
        Vec3d currentPos = client.player.getPos();
        return currentPos.distanceTo(lastPosition) > 0.001;
    }
    
    /**
     * Get movement velocity
     */
    public Vec3d getMovementVelocity() {
        if (client.player == null || lastPosition == null) {
            return Vec3d.ZERO;
        }
        
        Vec3d currentPos = client.player.getPos();
        return currentPos.subtract(lastPosition);
    }
    
    /**
     * Enable/disable movement controller
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        if (!enabled) {
            cancelAll();
        }
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Create a walk action to target position
     */
    public WalkAction createWalkAction(Vec3d target, boolean allowSprinting) {
        Vec3d current = getCurrentPosition();
        return new WalkAction(current, target, allowSprinting);
    }
    
    /**
     * Create a jump action to target position
     */
    public JumpAction createJumpAction(Vec3d target) {
        Vec3d current = getCurrentPosition();
        return new JumpAction(current, target);
    }
    
    /**
     * Execute immediate movement to position (bypasses queue)
     */
    public void moveToImmediate(Vec3d target, boolean allowSprinting) {
        cancelAll();
        WalkAction walkAction = createWalkAction(target, allowSprinting);
        queueAction(walkAction);
    }
    
    /**
     * Execute immediate jump to position (bypasses queue)
     */
    public void jumpToImmediate(Vec3d target) {
        cancelAll();
        JumpAction jumpAction = createJumpAction(target);
        queueAction(jumpAction);
    }
}
