package change.thisname.movement;

import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import change.thisname.physics.PhysicsSimulator;
import change.thisname.physics.MovementValidator;

import java.util.ArrayList;
import java.util.List;

/**
 * Calculates advanced parkour movements with physics-based feasibility validation
 */
public class ParkourCalculator {
    private final World world;
    private final PhysicsSimulator physicsSimulator;
    private final MovementValidator movementValidator;
    
    // Parkour parameters
    private static final double MAX_GAP_DISTANCE = 4.5;
    private static final double MAX_CLIMB_HEIGHT = 3.0;
    private static final double PRECISION_TOLERANCE = 0.3;
    private static final double MIN_MOMENTUM_DISTANCE = 3.0;
    
    public ParkourCalculator(World world, PhysicsSimulator physicsSimulator, MovementValidator movementValidator) {
        this.world = world;
        this.physicsSimulator = physicsSimulator;
        this.movementValidator = movementValidator;
    }
    
    /**
     * Calculate all possible parkour moves from a position
     */
    public List<ParkourMove> calculatePossibleMoves(Vec3d fromPosition, Vec3d goalPosition) {
        List<ParkourMove> possibleMoves = new ArrayList<>();
        
        // Calculate gap jumps
        possibleMoves.addAll(calculateGapJumps(fromPosition, goalPosition));
        
        // Calculate precision jumps
        possibleMoves.addAll(calculatePrecisionJumps(fromPosition, goalPosition));
        
        // Calculate wall climbs
        possibleMoves.addAll(calculateWallClimbs(fromPosition, goalPosition));
        
        // Calculate momentum jumps
        possibleMoves.addAll(calculateMomentumJumps(fromPosition, goalPosition));
        
        // Filter and rank moves
        return filterAndRankMoves(possibleMoves, goalPosition);
    }
    
    /**
     * Calculate gap jump possibilities
     */
    private List<ParkourMove> calculateGapJumps(Vec3d from, Vec3d goal) {
        List<ParkourMove> gapJumps = new ArrayList<>();
        
        // Search for gaps in multiple directions
        Vec3d[] directions = {
            new Vec3d(1, 0, 0), new Vec3d(-1, 0, 0),
            new Vec3d(0, 0, 1), new Vec3d(0, 0, -1),
            new Vec3d(1, 0, 1), new Vec3d(1, 0, -1),
            new Vec3d(-1, 0, 1), new Vec3d(-1, 0, -1)
        };
        
        for (Vec3d direction : directions) {
            for (double distance = 2.0; distance <= MAX_GAP_DISTANCE; distance += 0.5) {
                Vec3d targetPos = from.add(direction.multiply(distance));
                
                // Check if this is a valid gap jump
                if (isValidGapJump(from, targetPos)) {
                    double requiredVelocity = physicsSimulator.calculateRequiredJumpVelocity(from, targetPos);
                    double difficulty = calculateGapJumpDifficulty(from, targetPos);
                    double goalDistance = targetPos.distanceTo(goal);
                    
                    ParkourMove move = new ParkourMove(
                        from, targetPos, ParkourAction.ParkourType.GAP_JUMP,
                        requiredVelocity, direction, difficulty, goalDistance
                    );
                    
                    gapJumps.add(move);
                }
            }
        }
        
        return gapJumps;
    }
    
    /**
     * Calculate precision jump possibilities
     */
    private List<ParkourMove> calculatePrecisionJumps(Vec3d from, Vec3d goal) {
        List<ParkourMove> precisionJumps = new ArrayList<>();
        
        // Look for small platforms or precise landing spots
        BlockPos fromBlock = BlockPos.ofFloored(from);
        
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -2; y <= 3; y++) {
                    if (x == 0 && z == 0 && y == 0) continue;
                    
                    BlockPos targetBlock = fromBlock.add(x, y, z);
                    Vec3d targetPos = Vec3d.ofCenter(targetBlock);
                    
                    if (isValidPrecisionJump(from, targetPos)) {
                        double requiredVelocity = physicsSimulator.calculateRequiredJumpVelocity(from, targetPos);
                        double difficulty = calculatePrecisionJumpDifficulty(from, targetPos);
                        double goalDistance = targetPos.distanceTo(goal);
                        
                        Vec3d direction = targetPos.subtract(from).normalize();
                        
                        ParkourMove move = new ParkourMove(
                            from, targetPos, ParkourAction.ParkourType.PRECISION_JUMP,
                            requiredVelocity, direction, difficulty, goalDistance
                        );
                        
                        precisionJumps.add(move);
                    }
                }
            }
        }
        
        return precisionJumps;
    }
    
    /**
     * Calculate wall climb possibilities
     */
    private List<ParkourMove> calculateWallClimbs(Vec3d from, Vec3d goal) {
        List<ParkourMove> wallClimbs = new ArrayList<>();
        
        // Check for walls in cardinal directions
        Vec3d[] directions = {
            new Vec3d(1, 0, 0), new Vec3d(-1, 0, 0),
            new Vec3d(0, 0, 1), new Vec3d(0, 0, -1)
        };
        
        for (Vec3d direction : directions) {
            for (double height = 1.0; height <= MAX_CLIMB_HEIGHT; height += 0.5) {
                Vec3d wallPos = from.add(direction.multiply(0.5));
                Vec3d targetPos = wallPos.add(0, height, 0);
                
                if (isValidWallClimb(from, targetPos)) {
                    double difficulty = calculateWallClimbDifficulty(from, targetPos);
                    double goalDistance = targetPos.distanceTo(goal);
                    
                    ParkourMove move = new ParkourMove(
                        from, targetPos, ParkourAction.ParkourType.WALL_CLIMB,
                        0, direction, difficulty, goalDistance
                    );
                    
                    wallClimbs.add(move);
                }
            }
        }
        
        return wallClimbs;
    }
    
    /**
     * Calculate momentum jump possibilities
     */
    private List<ParkourMove> calculateMomentumJumps(Vec3d from, Vec3d goal) {
        List<ParkourMove> momentumJumps = new ArrayList<>();
        
        // Look for long-distance jump opportunities
        Vec3d[] directions = {
            new Vec3d(1, 0, 0), new Vec3d(-1, 0, 0),
            new Vec3d(0, 0, 1), new Vec3d(0, 0, -1),
            new Vec3d(1, 0, 1), new Vec3d(1, 0, -1),
            new Vec3d(-1, 0, 1), new Vec3d(-1, 0, -1)
        };
        
        for (Vec3d direction : directions) {
            for (double distance = MIN_MOMENTUM_DISTANCE; distance <= 6.0; distance += 0.5) {
                Vec3d targetPos = from.add(direction.multiply(distance));
                
                if (isValidMomentumJump(from, targetPos)) {
                    double requiredVelocity = physicsSimulator.calculateRequiredJumpVelocity(from, targetPos) * 1.2;
                    double difficulty = calculateMomentumJumpDifficulty(from, targetPos);
                    double goalDistance = targetPos.distanceTo(goal);
                    
                    ParkourMove move = new ParkourMove(
                        from, targetPos, ParkourAction.ParkourType.MOMENTUM_JUMP,
                        requiredVelocity, direction, difficulty, goalDistance
                    );
                    
                    momentumJumps.add(move);
                }
            }
        }
        
        return momentumJumps;
    }
    
    /**
     * Validate gap jump feasibility
     */
    private boolean isValidGapJump(Vec3d from, Vec3d to) {
        double distance = from.distanceTo(to);
        if (distance > MAX_GAP_DISTANCE || distance < 2.0) {
            return false;
        }
        
        return physicsSimulator.canPerformGapJump(from, to) && 
               movementValidator.canJumpTo(from, to);
    }
    
    /**
     * Validate precision jump feasibility
     */
    private boolean isValidPrecisionJump(Vec3d from, Vec3d to) {
        double distance = from.distanceTo(to);
        if (distance > 3.0 || distance < 1.0) {
            return false;
        }
        
        // Check if target requires precision (small platform)
        BlockPos targetBlock = BlockPos.ofFloored(to);
        if (!isPrecisionTarget(targetBlock)) {
            return false;
        }
        
        return movementValidator.canJumpTo(from, to);
    }
    
    /**
     * Validate wall climb feasibility
     */
    private boolean isValidWallClimb(Vec3d from, Vec3d to) {
        double heightDiff = to.y - from.y;
        if (heightDiff > MAX_CLIMB_HEIGHT || heightDiff < 1.0) {
            return false;
        }
        
        return physicsSimulator.canClimbWall(from, to);
    }
    
    /**
     * Validate momentum jump feasibility
     */
    private boolean isValidMomentumJump(Vec3d from, Vec3d to) {
        double horizontalDistance = Math.sqrt(
            Math.pow(to.x - from.x, 2) + Math.pow(to.z - from.z, 2)
        );
        
        if (horizontalDistance < MIN_MOMENTUM_DISTANCE || horizontalDistance > 6.0) {
            return false;
        }
        
        return movementValidator.canJumpTo(from, to);
    }
    
    /**
     * Check if target requires precision landing
     */
    private boolean isPrecisionTarget(BlockPos target) {
        // Check if surrounded by air or dangerous blocks
        int solidNeighbors = 0;
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x == 0 && z == 0) continue;
                BlockPos neighbor = target.add(x, 0, z);
                if (!world.getBlockState(neighbor).isAir()) {
                    solidNeighbors++;
                }
            }
        }
        
        return solidNeighbors <= 2; // Small platform
    }
    
    /**
     * Calculate difficulty scores for different jump types
     */
    private double calculateGapJumpDifficulty(Vec3d from, Vec3d to) {
        double distance = from.distanceTo(to);
        double heightDiff = Math.abs(to.y - from.y);
        return distance * 0.5 + heightDiff * 0.3;
    }
    
    private double calculatePrecisionJumpDifficulty(Vec3d from, Vec3d to) {
        double distance = from.distanceTo(to);
        return distance * 0.8 + 1.0; // Base precision difficulty
    }
    
    private double calculateWallClimbDifficulty(Vec3d from, Vec3d to) {
        double height = to.y - from.y;
        return height * 0.7 + 0.5;
    }
    
    private double calculateMomentumJumpDifficulty(Vec3d from, Vec3d to) {
        double distance = from.distanceTo(to);
        return distance * 0.4 + 1.5; // Base momentum difficulty
    }
    
    /**
     * Filter and rank parkour moves by feasibility and goal proximity
     */
    private List<ParkourMove> filterAndRankMoves(List<ParkourMove> moves, Vec3d goal) {
        return moves.stream()
            .filter(move -> move.getDifficulty() < 5.0) // Filter out extremely difficult moves
            .sorted((a, b) -> {
                // Sort by goal distance first, then difficulty
                int goalCompare = Double.compare(a.getGoalDistance(), b.getGoalDistance());
                if (goalCompare != 0) return goalCompare;
                return Double.compare(a.getDifficulty(), b.getDifficulty());
            })
            .limit(10) // Limit to top 10 moves
            .toList();
    }
    
    /**
     * Represents a calculated parkour move
     */
    public static class ParkourMove {
        private final Vec3d fromPosition;
        private final Vec3d toPosition;
        private final ParkourAction.ParkourType type;
        private final double requiredVelocity;
        private final Vec3d direction;
        private final double difficulty;
        private final double goalDistance;
        
        public ParkourMove(Vec3d fromPosition, Vec3d toPosition, ParkourAction.ParkourType type,
                          double requiredVelocity, Vec3d direction, double difficulty, double goalDistance) {
            this.fromPosition = fromPosition;
            this.toPosition = toPosition;
            this.type = type;
            this.requiredVelocity = requiredVelocity;
            this.direction = direction;
            this.difficulty = difficulty;
            this.goalDistance = goalDistance;
        }
        
        public ParkourAction createAction() {
            return new ParkourAction(fromPosition, toPosition, type, requiredVelocity, direction, 
                                   type == ParkourAction.ParkourType.PRECISION_JUMP);
        }
        
        // Getters
        public Vec3d getFromPosition() { return fromPosition; }
        public Vec3d getToPosition() { return toPosition; }
        public ParkourAction.ParkourType getType() { return type; }
        public double getRequiredVelocity() { return requiredVelocity; }
        public Vec3d getDirection() { return direction; }
        public double getDifficulty() { return difficulty; }
        public double getGoalDistance() { return goalDistance; }
    }
}
