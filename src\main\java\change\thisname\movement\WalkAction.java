package change.thisname.movement;

import net.minecraft.util.math.Vec3d;
import change.thisname.physics.PhysicsSimulator;

/**
 * Basic walking movement action
 */
public class WalkAction extends MovementAction {
    private final boolean allowSprinting;
    private Vec3d currentPosition;
    
    public WalkAction(Vec3d startPosition, Vec3d targetPosition, boolean allowSprinting) {
        super(startPosition, targetPosition, ActionType.WALK, 
              calculateDuration(startPosition, targetPosition, allowSprinting), 1.0);
        this.allowSprinting = allowSprinting;
        this.currentPosition = startPosition;
    }
    
    private static double calculateDuration(Vec3d start, Vec3d target, boolean sprinting) {
        double distance = start.distanceTo(target);
        double speed = sprinting ? PhysicsSimulator.SPRINT_SPEED : PhysicsSimulator.WALK_SPEED;
        return distance / speed / 20.0; // Convert ticks to seconds
    }
    
    @Override
    public boolean validatePhysics(PhysicsSimulator physicsSimulator) {
        // Check if path is clear
        if (physicsSimulator.checkCollision(startPosition, targetPosition)) {
            setValidationError("Path blocked by collision");
            return false;
        }
        
        // Check if movement is within reasonable speed limits
        double distance = startPosition.distanceTo(targetPosition);
        double maxDistance = (allowSprinting ? PhysicsSimulator.SPRINT_SPEED : PhysicsSimulator.WALK_SPEED) * duration * 20;
        
        if (distance > maxDistance * 1.1) { // 10% tolerance
            setValidationError("Distance too far for walking speed");
            return false;
        }
        
        setPhysicsValidated(true);
        return true;
    }
    
    @Override
    public ActionResult executeTick(MovementController controller) {
        if (!started) {
            start();
        }
        
        updateProgress();
        
        // Calculate new position
        currentPosition = interpolatePosition();
        
        // Apply inputs
        InputState inputs = getRequiredInputs();
        controller.applyInputs(inputs);
        
        return new ActionResult(true, currentPosition, completed);
    }
    
    @Override
    public Vec3d getCurrentPosition() {
        return currentPosition;
    }
    
    @Override
    public InputState getRequiredInputs() {
        if (completed) {
            return new InputState();
        }
        
        // Calculate movement direction
        Vec3d direction = targetPosition.subtract(currentPosition).normalize();
        
        InputState inputs = new InputState();
        
        // Determine primary movement direction
        if (Math.abs(direction.z) > Math.abs(direction.x)) {
            if (direction.z > 0) {
                inputs.forward(true);
            } else {
                inputs.backward(true);
            }
        } else {
            if (direction.x > 0) {
                inputs.right(true);
            } else {
                inputs.left(true);
            }
        }
        
        // Add sprinting if allowed and beneficial
        if (allowSprinting && startPosition.distanceTo(targetPosition) > 3.0) {
            inputs.sprint(true);
        }
        
        return inputs;
    }
}
