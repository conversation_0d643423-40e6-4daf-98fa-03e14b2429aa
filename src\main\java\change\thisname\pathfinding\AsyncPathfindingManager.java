package change.thisname.pathfinding;

import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import change.thisname.physics.PhysicsSimulator;
import change.thisname.physics.MovementValidator;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * Asynchronous pathfinding manager that runs pathfinding calculations
 * on separate threads to avoid blocking the main game thread.
 */
public class AsyncPathfindingManager {
    private final ExecutorService pathfindingExecutor;
    private final ScheduledExecutorService scheduledExecutor;
    private final AStarPathfinder pathfinder;
    
    // Current pathfinding state
    private final AtomicReference<PathfindingTask> currentTask;
    private final AtomicInteger taskIdCounter;
    
    // Thread safety
    private final Object taskLock = new Object();
    
    public AsyncPathfindingManager(World world, PhysicsSimulator physicsSimulator, MovementValidator movementValidator) {
        // Create thread pool for pathfinding calculations
        this.pathfindingExecutor = Executors.newFixedThreadPool(2, r -> {
            Thread t = new Thread(r, "Pathfinding-Worker");
            t.setDaemon(true);
            t.setPriority(Thread.NORM_PRIORITY - 1); // Lower priority than main thread
            return t;
        });
        
        // Scheduled executor for periodic tasks
        this.scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "Pathfinding-Scheduler");
            t.setDaemon(true);
            return t;
        });
        
        this.pathfinder = new AStarPathfinder(world, physicsSimulator, movementValidator);
        this.currentTask = new AtomicReference<>();
        this.taskIdCounter = new AtomicInteger(0);
    }
    
    /**
     * Request pathfinding from start to goal asynchronously
     */
    public PathfindingTask findPathAsync(Vec3d start, Vec3d goal, Consumer<PathfindingResult> callback) {
        return findPathAsync(start, goal, new PathfindingOptions(), callback);
    }
    
    /**
     * Request pathfinding with options asynchronously
     */
    public PathfindingTask findPathAsync(Vec3d start, Vec3d goal, PathfindingOptions options, Consumer<PathfindingResult> callback) {
        synchronized (taskLock) {
            // Cancel current task if running
            PathfindingTask oldTask = currentTask.get();
            if (oldTask != null && !oldTask.isDone()) {
                oldTask.cancel();
            }
            
            // Create new task
            PathfindingTask task = new PathfindingTask(
                taskIdCounter.incrementAndGet(),
                start, goal, options, callback
            );
            
            currentTask.set(task);
            
            // Submit to executor
            CompletableFuture<PathfindingResult> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return pathfinder.findPath(start, goal, options);
                } catch (Exception e) {
                    PathfindingResult errorResult = new PathfindingResult();
                    errorResult.setSuccess(false);
                    errorResult.setFailureReason("Exception during pathfinding: " + e.getMessage());
                    return errorResult;
                }
            }, pathfindingExecutor);
            
            task.setFuture(future);
            
            // Handle completion
            future.whenComplete((result, throwable) -> {
                if (!task.isCancelled()) {
                    if (throwable != null) {
                        PathfindingResult errorResult = new PathfindingResult();
                        errorResult.setSuccess(false);
                        errorResult.setFailureReason("Async error: " + throwable.getMessage());
                        callback.accept(errorResult);
                    } else {
                        callback.accept(result);
                    }
                }
                task.markCompleted();
            });
            
            return task;
        }
    }
    
    /**
     * Request continuous pathfinding that updates periodically
     */
    public ContinuousPathfindingTask findPathContinuous(Vec3d start, Vec3d goal, PathfindingOptions options, 
                                                       Consumer<PathfindingResult> callback, long updateIntervalMs) {
        ContinuousPathfindingTask continuousTask = new ContinuousPathfindingTask(
            taskIdCounter.incrementAndGet(), start, goal, options, callback, updateIntervalMs
        );
        
        // Schedule periodic updates
        ScheduledFuture<?> scheduledFuture = scheduledExecutor.scheduleAtFixedRate(() -> {
            if (!continuousTask.isCancelled()) {
                findPathAsync(continuousTask.getCurrentStart(), goal, options, result -> {
                    if (!continuousTask.isCancelled()) {
                        callback.accept(result);
                    }
                });
            }
        }, 0, updateIntervalMs, TimeUnit.MILLISECONDS);
        
        continuousTask.setScheduledFuture(scheduledFuture);
        return continuousTask;
    }
    
    /**
     * Cancel current pathfinding task
     */
    public void cancelCurrentTask() {
        PathfindingTask task = currentTask.get();
        if (task != null) {
            task.cancel();
        }
    }
    
    /**
     * Get current pathfinding task
     */
    public PathfindingTask getCurrentTask() {
        return currentTask.get();
    }
    
    /**
     * Check if pathfinding is currently running
     */
    public boolean isPathfindingActive() {
        PathfindingTask task = currentTask.get();
        return task != null && !task.isDone();
    }
    
    /**
     * Shutdown the pathfinding manager
     */
    public void shutdown() {
        cancelCurrentTask();
        pathfindingExecutor.shutdown();
        scheduledExecutor.shutdown();
        
        try {
            if (!pathfindingExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                pathfindingExecutor.shutdownNow();
            }
            if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            pathfindingExecutor.shutdownNow();
            scheduledExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Represents a pathfinding task
     */
    public static class PathfindingTask {
        private final int taskId;
        private final Vec3d start;
        private final Vec3d goal;
        private final PathfindingOptions options;
        private final Consumer<PathfindingResult> callback;
        private final long creationTime;
        
        private CompletableFuture<PathfindingResult> future;
        private volatile boolean cancelled = false;
        private volatile boolean completed = false;
        
        public PathfindingTask(int taskId, Vec3d start, Vec3d goal, PathfindingOptions options, Consumer<PathfindingResult> callback) {
            this.taskId = taskId;
            this.start = start;
            this.goal = goal;
            this.options = options;
            this.callback = callback;
            this.creationTime = System.currentTimeMillis();
        }
        
        public void setFuture(CompletableFuture<PathfindingResult> future) {
            this.future = future;
        }
        
        public void cancel() {
            cancelled = true;
            if (future != null) {
                future.cancel(true);
            }
        }
        
        public void markCompleted() {
            completed = true;
        }
        
        public boolean isCancelled() { return cancelled; }
        public boolean isCompleted() { return completed; }
        public boolean isDone() { return cancelled || completed; }
        
        // Getters
        public int getTaskId() { return taskId; }
        public Vec3d getStart() { return start; }
        public Vec3d getGoal() { return goal; }
        public PathfindingOptions getOptions() { return options; }
        public long getCreationTime() { return creationTime; }
        public long getAge() { return System.currentTimeMillis() - creationTime; }
    }
    
    /**
     * Continuous pathfinding task that updates periodically
     */
    public static class ContinuousPathfindingTask extends PathfindingTask {
        private final long updateIntervalMs;
        private ScheduledFuture<?> scheduledFuture;
        private volatile Vec3d currentStart;
        
        public ContinuousPathfindingTask(int taskId, Vec3d start, Vec3d goal, PathfindingOptions options, 
                                       Consumer<PathfindingResult> callback, long updateIntervalMs) {
            super(taskId, start, goal, options, callback);
            this.updateIntervalMs = updateIntervalMs;
            this.currentStart = start;
        }
        
        public void setScheduledFuture(ScheduledFuture<?> scheduledFuture) {
            this.scheduledFuture = scheduledFuture;
        }
        
        public void updateCurrentStart(Vec3d newStart) {
            this.currentStart = newStart;
        }
        
        @Override
        public void cancel() {
            super.cancel();
            if (scheduledFuture != null) {
                scheduledFuture.cancel(true);
            }
        }
        
        public Vec3d getCurrentStart() { return currentStart; }
        public long getUpdateIntervalMs() { return updateIntervalMs; }
    }
}
