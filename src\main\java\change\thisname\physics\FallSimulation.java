package change.thisname.physics;

import net.minecraft.util.math.Vec3d;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a fall simulation with damage calculation
 */
public class FallSimulation {
    private final Vec3d startPosition;
    private final List<FallPoint> fallPoints;
    
    private Vec3d landingPoint;
    private Vec3d landingVelocity;
    private double fallDistance;
    private double fallTime;
    private int damageAmount;
    private boolean isSafe;
    
    public FallSimulation(Vec3d start) {
        this.startPosition = start;
        this.fallPoints = new ArrayList<>();
        this.fallDistance = 0;
        this.fallTime = 0;
        this.damageAmount = 0;
        this.isSafe = true;
    }
    
    /**
     * Add a point to the fall simulation
     */
    public void addPoint(Vec3d position, Vec3d velocity, int tick) {
        FallPoint point = new FallPoint(position, velocity, tick);
        fallPoints.add(point);
        fallTime = tick / 20.0; // Convert to seconds
    }
    
    /**
     * Calculate fall damage based on Minecraft mechanics
     */
    public void calculateDamage() {
        if (landingPoint == null) {
            return;
        }
        
        fallDistance = startPosition.y - landingPoint.y;
        
        // Minecraft fall damage calculation
        // No damage for falls less than 3 blocks
        if (fallDistance <= 3.0) {
            damageAmount = 0;
            isSafe = true;
            return;
        }
        
        // 1 damage per block after 3 blocks
        damageAmount = (int) Math.floor(fallDistance - 3.0);
        
        // Falls over 20 blocks are usually fatal (20 hearts = 40 health points)
        if (damageAmount >= 20) {
            isSafe = false;
        } else if (damageAmount >= 10) {
            isSafe = false; // Dangerous but not necessarily fatal
        } else {
            isSafe = true; // Manageable damage
        }
    }
    
    /**
     * Get fall velocity at landing
     */
    public double getLandingSpeed() {
        if (landingVelocity == null) {
            return 0;
        }
        return landingVelocity.length();
    }
    
    /**
     * Check if fall would be survivable with current health
     */
    public boolean isSurvivable(int currentHealth) {
        return currentHealth > damageAmount;
    }
    
    /**
     * Get the point where maximum velocity is reached
     */
    public FallPoint getTerminalVelocityPoint() {
        FallPoint maxVelocityPoint = null;
        double maxSpeed = 0;
        
        for (FallPoint point : fallPoints) {
            double speed = point.getSpeed();
            if (speed > maxSpeed) {
                maxSpeed = speed;
                maxVelocityPoint = point;
            }
        }
        
        return maxVelocityPoint;
    }
    
    /**
     * Get simplified fall path for rendering
     */
    public List<Vec3d> getSimplifiedPath(int step) {
        List<Vec3d> simplified = new ArrayList<>();
        for (int i = 0; i < fallPoints.size(); i += step) {
            simplified.add(fallPoints.get(i).getPosition());
        }
        return simplified;
    }
    
    /**
     * Check if fall passes through dangerous areas
     */
    public boolean passesThroughDanger(Vec3d dangerCenter, double dangerRadius) {
        for (FallPoint point : fallPoints) {
            if (point.getPosition().distanceTo(dangerCenter) <= dangerRadius) {
                return true;
            }
        }
        return false;
    }
    
    // Getters and setters
    public Vec3d getStartPosition() { return startPosition; }
    public List<FallPoint> getFallPoints() { return fallPoints; }
    public Vec3d getLandingPoint() { return landingPoint; }
    public void setLandingPoint(Vec3d landingPoint) { this.landingPoint = landingPoint; }
    public Vec3d getLandingVelocity() { return landingVelocity; }
    public void setLandingVelocity(Vec3d landingVelocity) { this.landingVelocity = landingVelocity; }
    public double getFallDistance() { return fallDistance; }
    public double getFallTime() { return fallTime; }
    public int getDamageAmount() { return damageAmount; }
    public boolean isSafe() { return isSafe; }
    
    /**
     * Represents a single point in the fall
     */
    public static class FallPoint {
        private final Vec3d position;
        private final Vec3d velocity;
        private final int tick;
        
        public FallPoint(Vec3d position, Vec3d velocity, int tick) {
            this.position = position;
            this.velocity = velocity;
            this.tick = tick;
        }
        
        public Vec3d getPosition() { return position; }
        public Vec3d getVelocity() { return velocity; }
        public int getTick() { return tick; }
        public double getSpeed() { return velocity.length(); }
        public double getTime() { return tick / 20.0; } // Convert ticks to seconds
    }
}
