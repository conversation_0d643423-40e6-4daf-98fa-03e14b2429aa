package change.thisname.bot;

import net.minecraft.client.MinecraftClient;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import change.thisname.pathfinding.AsyncPathfindingManager;
import change.thisname.pathfinding.PathfindingOptions;
import change.thisname.pathfinding.PathfindingResult;
import change.thisname.physics.PhysicsSimulator;
import change.thisname.physics.MovementValidator;
import change.thisname.movement.MovementController;
import change.thisname.movement.MovementAction;
import change.thisname.movement.WalkAction;
import change.thisname.movement.JumpAction;
import change.thisname.movement.ParkourAction;
import change.thisname.movement.ParkourCalculator;
import change.thisname.render.PathfindingRenderer;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Main bot controller that coordinates pathfinding, physics simulation, and movement execution
 */
public class PathfindingBot {
    private final MinecraftClient client;
    private final World world;
    
    // Core systems
    private final PhysicsSimulator physicsSimulator;
    private final MovementValidator movementValidator;
    private final AsyncPathfindingManager pathfindingManager;
    private final MovementController movementController;
    private final ParkourCalculator parkourCalculator;
    private final PathfindingRenderer renderer;
    
    // Bot state
    private final AtomicReference<BotState> currentState;
    private Vec3d currentGoal;
    private PathfindingResult currentPath;
    private PathfindingOptions pathfindingOptions;
    
    // Dynamic environment handling
    private Vec3d lastPlayerPosition;
    private long lastPathUpdate;
    private static final long PATH_UPDATE_INTERVAL = 1000; // 1 second
    private static final double POSITION_CHANGE_THRESHOLD = 2.0; // 2 blocks
    
    public PathfindingBot(MinecraftClient client) {
        this.client = client;
        this.world = client.world;
        
        // Initialize core systems
        this.physicsSimulator = new PhysicsSimulator(world);
        this.movementValidator = new MovementValidator(world, physicsSimulator);
        this.pathfindingManager = new AsyncPathfindingManager(world, physicsSimulator, movementValidator);
        this.movementController = new MovementController(client, physicsSimulator);
        this.parkourCalculator = new ParkourCalculator(world, physicsSimulator, movementValidator);
        this.renderer = new PathfindingRenderer(client);
        
        // Initialize state
        this.currentState = new AtomicReference<>(BotState.IDLE);
        this.pathfindingOptions = new PathfindingOptions();
        this.lastPlayerPosition = Vec3d.ZERO;
        this.lastPathUpdate = 0;
    }
    
    /**
     * Main bot tick - called every game tick
     */
    public void tick() {
        if (client.player == null || client.world == null) {
            return;
        }
        
        // Update movement controller
        movementController.tick();
        
        // Handle dynamic environment changes
        handleDynamicEnvironment();
        
        // Update bot state
        updateBotState();
    }
    
    /**
     * Set goal position and start pathfinding
     */
    public void setGoal(Vec3d goal) {
        this.currentGoal = goal;
        this.renderer.updateGoalPosition(goal);
        
        if (currentState.get() != BotState.DISABLED) {
            startPathfinding();
        }
    }
    
    /**
     * Start pathfinding to current goal
     */
    private void startPathfinding() {
        if (currentGoal == null || client.player == null) {
            return;
        }
        
        Vec3d startPosition = client.player.getPos();
        currentState.set(BotState.PATHFINDING);
        
        // Start async pathfinding
        pathfindingManager.findPathAsync(startPosition, currentGoal, pathfindingOptions, this::onPathfindingComplete);
        lastPathUpdate = System.currentTimeMillis();
    }
    
    /**
     * Handle pathfinding completion
     */
    private void onPathfindingComplete(PathfindingResult result) {
        this.currentPath = result;
        this.renderer.updatePathfindingResult(result);
        
        if (result.isSuccess()) {
            currentState.set(BotState.EXECUTING);
            executePathfinding(result);
        } else {
            currentState.set(BotState.FAILED);
            System.err.println("Pathfinding failed: " + result.getFailureReason());
        }
    }
    
    /**
     * Execute pathfinding result
     */
    private void executePathfinding(PathfindingResult result) {
        List<Vec3d> simplifiedPath = result.getSimplifiedPath();
        
        if (simplifiedPath.isEmpty()) {
            currentState.set(BotState.IDLE);
            return;
        }
        
        // Convert path to movement actions
        Vec3d currentPos = client.player.getPos();
        
        for (int i = 1; i < simplifiedPath.size(); i++) {
            Vec3d targetPos = simplifiedPath.get(i);
            MovementAction action = createMovementAction(currentPos, targetPos);
            
            if (action != null) {
                try {
                    movementController.queueAction(action);
                } catch (IllegalArgumentException e) {
                    System.err.println("Failed to queue movement action: " + e.getMessage());
                    currentState.set(BotState.FAILED);
                    return;
                }
            }
            
            currentPos = targetPos;
        }
    }
    
    /**
     * Create appropriate movement action for path segment
     */
    private MovementAction createMovementAction(Vec3d from, Vec3d to) {
        double distance = from.distanceTo(to);
        double heightDiff = to.y - from.y;
        
        // Determine action type based on movement characteristics
        if (heightDiff > 1.0 || distance > 3.0) {
            // Check if parkour is needed
            if (pathfindingOptions.isAllowParkour()) {
                List<ParkourCalculator.ParkourMove> parkourMoves = 
                    parkourCalculator.calculatePossibleMoves(from, to);
                
                if (!parkourMoves.isEmpty()) {
                    return parkourMoves.get(0).createAction();
                }
            }
            
            // Regular jump
            if (pathfindingOptions.isAllowJumping()) {
                return new JumpAction(from, to);
            }
        }
        
        // Regular walk/sprint
        return new WalkAction(from, to, pathfindingOptions.isAllowSprinting());
    }
    
    /**
     * Handle dynamic environment changes
     */
    private void handleDynamicEnvironment() {
        if (client.player == null) {
            return;
        }
        
        Vec3d currentPlayerPos = client.player.getPos();
        
        // Check if player position changed significantly
        if (lastPlayerPosition.distanceTo(currentPlayerPos) > POSITION_CHANGE_THRESHOLD) {
            lastPlayerPosition = currentPlayerPos;
            
            // Recalculate path if we're actively pathfinding
            if (currentState.get() == BotState.EXECUTING && currentGoal != null) {
                long timeSinceLastUpdate = System.currentTimeMillis() - lastPathUpdate;
                if (timeSinceLastUpdate > PATH_UPDATE_INTERVAL) {
                    startPathfinding();
                }
            }
        }
    }
    
    /**
     * Update bot state based on current conditions
     */
    private void updateBotState() {
        BotState state = currentState.get();
        
        switch (state) {
            case EXECUTING:
                // Check if movement is complete
                if (!movementController.isActive()) {
                    if (currentGoal != null && client.player != null) {
                        double distanceToGoal = client.player.getPos().distanceTo(currentGoal);
                        if (distanceToGoal <= 1.0) {
                            currentState.set(BotState.COMPLETED);
                        } else {
                            // Goal not reached, try pathfinding again
                            startPathfinding();
                        }
                    } else {
                        currentState.set(BotState.IDLE);
                    }
                }
                break;
                
            case FAILED:
                // Auto-retry after a delay
                if (System.currentTimeMillis() - lastPathUpdate > 5000) { // 5 second delay
                    if (currentGoal != null) {
                        startPathfinding();
                    } else {
                        currentState.set(BotState.IDLE);
                    }
                }
                break;
                
            case COMPLETED:
                // Reset to idle after completion
                currentGoal = null;
                renderer.clearAll();
                currentState.set(BotState.IDLE);
                break;
        }
    }
    
    /**
     * Enable/disable the bot
     */
    public void setEnabled(boolean enabled) {
        if (enabled) {
            if (currentState.get() == BotState.DISABLED) {
                currentState.set(BotState.IDLE);
                movementController.setEnabled(true);
            }
        } else {
            currentState.set(BotState.DISABLED);
            movementController.setEnabled(false);
            pathfindingManager.cancelCurrentTask();
            renderer.clearAll();
        }
    }
    
    /**
     * Stop current pathfinding and movement
     */
    public void stop() {
        pathfindingManager.cancelCurrentTask();
        movementController.cancelAll();
        currentGoal = null;
        renderer.clearAll();
        currentState.set(BotState.IDLE);
    }
    
    /**
     * Emergency stop - immediately halt all movement
     */
    public void emergencyStop() {
        pathfindingManager.cancelCurrentTask();
        movementController.cancelAll();
        movementController.clearInputs();
        currentGoal = null;
        renderer.clearAll();
        currentState.set(BotState.IDLE);
    }
    
    // Getters
    public BotState getCurrentState() { return currentState.get(); }
    public Vec3d getCurrentGoal() { return currentGoal; }
    public PathfindingResult getCurrentPath() { return currentPath; }
    public PathfindingOptions getPathfindingOptions() { return pathfindingOptions; }
    public PathfindingRenderer getRenderer() { return renderer; }
    public MovementController getMovementController() { return movementController; }
    public AsyncPathfindingManager getPathfindingManager() { return pathfindingManager; }
    
    // Setters
    public void setPathfindingOptions(PathfindingOptions options) { 
        this.pathfindingOptions = options; 
    }
    
    /**
     * Shutdown the bot and cleanup resources
     */
    public void shutdown() {
        emergencyStop();
        pathfindingManager.shutdown();
    }
    
    /**
     * Bot execution states
     */
    public enum BotState {
        IDLE,           // Bot is idle, waiting for commands
        PATHFINDING,    // Bot is calculating path
        EXECUTING,      // Bot is executing movement
        COMPLETED,      // Bot reached goal
        FAILED,         // Pathfinding or movement failed
        DISABLED        // Bot is disabled
    }
}
