package change.thisname.physics;

import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import net.minecraft.block.BlockState;
import net.minecraft.util.math.Box;
import net.minecraft.block.Blocks;

/**
 * Validates movement feasibility using physics simulation and world state
 */
public class MovementValidator {
    private final World world;
    private final PhysicsSimulator physicsSimulator;
    
    // Movement validation parameters
    private static final double STEP_HEIGHT = 0.6; // Maximum step height
    private static final double MIN_CEILING_HEIGHT = 2.0; // Minimum ceiling clearance
    private static final double WATER_SLOWDOWN = 0.8; // Movement speed in water
    private static final double LAVA_DAMAGE_THRESHOLD = 1.0; // Blocks from lava to take damage
    
    public MovementValidator(World world, PhysicsSimulator physicsSimulator) {
        this.world = world;
        this.physicsSimulator = physicsSimulator;
    }
    
    /**
     * Check if player can move from one position to another
     */
    public boolean canMoveTo(Vec3d from, Vec3d to) {
        // Basic collision check
        if (physicsSimulator.checkCollision(from, to)) {
            return false;
        }
        
        // Check step height
        if (!canStepUp(from, to)) {
            return false;
        }
        
        // Check ceiling clearance
        if (!hasCeilingClearance(to)) {
            return false;
        }
        
        // Check for dangerous blocks
        if (isDangerousPosition(to)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if player can jump to target position
     */
    public boolean canJumpTo(Vec3d from, Vec3d to) {
        // Check if jump is physically possible
        JumpTrajectory trajectory = physicsSimulator.simulateJump(from, to);
        
        if (!trajectory.isSuccess() || trajectory.hasCollision()) {
            return false;
        }
        
        // Check landing position safety
        Vec3d landingPoint = trajectory.getLandingPoint();
        if (landingPoint == null || isDangerousPosition(landingPoint)) {
            return false;
        }
        
        // Check if there's ground to land on
        if (!hasGroundSupport(landingPoint)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if player can step up from current position
     */
    private boolean canStepUp(Vec3d from, Vec3d to) {
        double heightDiff = to.y - from.y;
        
        // Can't step up more than step height
        if (heightDiff > STEP_HEIGHT) {
            return false;
        }
        
        // Can't step down too far without falling
        if (heightDiff < -STEP_HEIGHT) {
            // Check if there's ground support at target
            return hasGroundSupport(to);
        }
        
        return true;
    }
    
    /**
     * Check if position has adequate ceiling clearance
     */
    private boolean hasCeilingClearance(Vec3d position) {
        Vec3d headPosition = position.add(0, PhysicsSimulator.PLAYER_HEIGHT, 0);
        BlockPos headBlock = BlockPos.ofFloored(headPosition);
        
        // Check blocks above player head
        for (int y = 0; y < MIN_CEILING_HEIGHT; y++) {
            BlockPos checkPos = headBlock.up(y);
            BlockState state = world.getBlockState(checkPos);
            
            if (!state.isAir() && !isPassableBlock(state)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check if position has ground support
     */
    private boolean hasGroundSupport(Vec3d position) {
        BlockPos groundPos = BlockPos.ofFloored(position.subtract(0, 0.1, 0));
        BlockState groundState = world.getBlockState(groundPos);
        
        return !groundState.isAir() && !isPassableBlock(groundState);
    }
    
    /**
     * Check if position is dangerous (lava, void, etc.)
     */
    private boolean isDangerousPosition(Vec3d position) {
        BlockPos blockPos = BlockPos.ofFloored(position);
        
        // Check for lava
        if (isNearLava(position)) {
            return true;
        }
        
        // Check for void (below Y=0)
        if (position.y < 0) {
            return true;
        }
        
        // Check for cactus or other damaging blocks
        BlockState state = world.getBlockState(blockPos);
        if (isDamageBlock(state)) {
            return true;
        }
        
        // Check for fall damage
        FallSimulation fallSim = physicsSimulator.simulateFall(position);
        if (!fallSim.isSafe()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if position is near lava
     */
    private boolean isNearLava(Vec3d position) {
        BlockPos center = BlockPos.ofFloored(position);
        
        // Check surrounding blocks for lava
        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    BlockPos checkPos = center.add(x, y, z);
                    BlockState state = world.getBlockState(checkPos);
                    
                    if (state.isOf(Blocks.LAVA)) {
                        double distance = position.distanceTo(Vec3d.ofCenter(checkPos));
                        if (distance <= LAVA_DAMAGE_THRESHOLD) {
                            return true;
                        }
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Check if block causes damage
     */
    private boolean isDamageBlock(BlockState state) {
        return state.isOf(Blocks.CACTUS) || 
               state.isOf(Blocks.LAVA) || 
               state.isOf(Blocks.FIRE) ||
               state.isOf(Blocks.MAGMA_BLOCK) ||
               state.isOf(Blocks.SWEET_BERRY_BUSH);
    }
    
    /**
     * Check if block is passable (can walk through)
     */
    private boolean isPassableBlock(BlockState state) {
        return state.isAir() ||
               state.isOf(Blocks.WATER) ||
               state.isOf(Blocks.TALL_GRASS) ||
               state.isOf(Blocks.SHORT_GRASS) ||
               state.isOf(Blocks.FERN);
    }
    
    /**
     * Validate a complex movement sequence
     */
    public MovementValidationResult validateMovementSequence(Vec3d start, Vec3d[] waypoints, Vec3d goal) {
        MovementValidationResult result = new MovementValidationResult();
        result.setValid(true);
        
        Vec3d currentPos = start;
        
        // Validate each segment
        for (int i = 0; i < waypoints.length; i++) {
            Vec3d nextPos = waypoints[i];
            
            if (!canMoveTo(currentPos, nextPos)) {
                result.setValid(false);
                result.setFailurePoint(nextPos);
                result.setFailureReason("Cannot move from " + currentPos + " to " + nextPos);
                return result;
            }
            
            currentPos = nextPos;
        }
        
        // Validate final segment to goal
        if (!canMoveTo(currentPos, goal)) {
            result.setValid(false);
            result.setFailurePoint(goal);
            result.setFailureReason("Cannot reach goal from " + currentPos);
            return result;
        }
        
        result.setTotalDistance(calculateTotalDistance(start, waypoints, goal));
        result.setEstimatedTime(calculateEstimatedTime(start, waypoints, goal));
        
        return result;
    }
    
    /**
     * Calculate total distance of movement sequence
     */
    private double calculateTotalDistance(Vec3d start, Vec3d[] waypoints, Vec3d goal) {
        double totalDistance = 0;
        Vec3d currentPos = start;
        
        for (Vec3d waypoint : waypoints) {
            totalDistance += currentPos.distanceTo(waypoint);
            currentPos = waypoint;
        }
        
        totalDistance += currentPos.distanceTo(goal);
        return totalDistance;
    }
    
    /**
     * Calculate estimated time for movement sequence
     */
    private double calculateEstimatedTime(Vec3d start, Vec3d[] waypoints, Vec3d goal) {
        double totalTime = 0;
        Vec3d currentPos = start;
        
        for (Vec3d waypoint : waypoints) {
            totalTime += physicsSimulator.calculateMovementTime(currentPos, waypoint, true);
            currentPos = waypoint;
        }
        
        totalTime += physicsSimulator.calculateMovementTime(currentPos, goal, true);
        return totalTime;
    }
    
    /**
     * Result of movement validation
     */
    public static class MovementValidationResult {
        private boolean valid;
        private Vec3d failurePoint;
        private String failureReason;
        private double totalDistance;
        private double estimatedTime;
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public Vec3d getFailurePoint() { return failurePoint; }
        public void setFailurePoint(Vec3d failurePoint) { this.failurePoint = failurePoint; }
        public String getFailureReason() { return failureReason; }
        public void setFailureReason(String failureReason) { this.failureReason = failureReason; }
        public double getTotalDistance() { return totalDistance; }
        public void setTotalDistance(double totalDistance) { this.totalDistance = totalDistance; }
        public double getEstimatedTime() { return estimatedTime; }
        public void setEstimatedTime(double estimatedTime) { this.estimatedTime = estimatedTime; }
    }
}
