package change.thisname.physics;

import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import net.minecraft.block.BlockState;
import net.minecraft.util.math.Box;

/**
 * Physics simulation engine that accurately replicates Minecraft's physics
 * for movement validation, jump trajectories, and collision detection.
 */
public class PhysicsSimulator {
    private final World world;
    
    // Minecraft physics constants
    public static final double GRAVITY = 0.08; // Blocks per tick squared
    public static final double DRAG = 0.02; // Air resistance
    public static final double JUMP_VELOCITY = 0.42; // Initial jump velocity
    public static final double WALK_SPEED = 0.1; // Blocks per tick
    public static final double SPRINT_SPEED = 0.13; // Blocks per tick
    public static final double PLAYER_WIDTH = 0.6;
    public static final double PLAYER_HEIGHT = 1.8;
    public static final double PLAYER_EYE_HEIGHT = 1.62;
    
    // Physics simulation parameters
    public static final int MAX_SIMULATION_TICKS = 100;
    public static final double COLLISION_TOLERANCE = 0.001;
    public static final double GROUND_TOLERANCE = 0.1;
    
    public PhysicsSimulator(World world) {
        this.world = world;
    }
    
    /**
     * Simulate a jump trajectory from start to target position
     */
    public JumpTrajectory simulateJump(Vec3d start, Vec3d target) {
        return simulateJump(start, target, JUMP_VELOCITY);
    }
    
    /**
     * Simulate a jump trajectory with custom initial velocity
     */
    public JumpTrajectory simulateJump(Vec3d start, Vec3d target, double initialVelocity) {
        JumpTrajectory trajectory = new JumpTrajectory(start, target);
        
        Vec3d position = start;
        Vec3d velocity = calculateInitialJumpVelocity(start, target, initialVelocity);
        
        for (int tick = 0; tick < MAX_SIMULATION_TICKS; tick++) {
            // Store trajectory point
            trajectory.addPoint(position, velocity, tick);
            
            // Apply gravity
            velocity = velocity.subtract(0, GRAVITY, 0);
            
            // Apply drag
            velocity = velocity.multiply(1 - DRAG);
            
            // Update position
            Vec3d newPosition = position.add(velocity);
            
            // Check for collision
            if (checkCollision(position, newPosition)) {
                trajectory.setCollisionPoint(newPosition);
                trajectory.setSuccess(false);
                break;
            }
            
            // Check if we've reached the target area
            if (isNearTarget(newPosition, target, 1.0)) {
                trajectory.setLandingPoint(newPosition);
                trajectory.setSuccess(true);
                break;
            }
            
            // Check if we've fallen too far
            if (newPosition.y < start.y - 20) {
                trajectory.setSuccess(false);
                break;
            }
            
            position = newPosition;
        }
        
        trajectory.calculateMetrics();
        return trajectory;
    }
    
    /**
     * Calculate required initial velocity for a jump to reach target
     */
    public double calculateRequiredJumpVelocity(Vec3d start, Vec3d target) {
        double horizontalDistance = Math.sqrt(
            Math.pow(target.x - start.x, 2) + Math.pow(target.z - start.z, 2)
        );
        double verticalDistance = target.y - start.y;
        
        // Use kinematic equations to calculate required velocity
        // Assuming optimal angle for maximum range
        double g = GRAVITY * 20; // Convert to blocks per second squared
        double v0 = Math.sqrt(g * horizontalDistance * horizontalDistance / 
                             (horizontalDistance - 2 * verticalDistance));
        
        return Math.max(JUMP_VELOCITY, v0 / 20); // Convert back to blocks per tick
    }
    
    /**
     * Calculate initial jump velocity vector
     */
    private Vec3d calculateInitialJumpVelocity(Vec3d start, Vec3d target, double jumpVelocity) {
        Vec3d horizontal = target.subtract(start);
        horizontal = new Vec3d(horizontal.x, 0, horizontal.z).normalize();
        
        double horizontalSpeed = WALK_SPEED * 2; // Jump gives horizontal boost
        Vec3d horizontalVelocity = horizontal.multiply(horizontalSpeed);
        
        return horizontalVelocity.add(0, jumpVelocity, 0);
    }
    
    /**
     * Check if a gap jump is possible
     */
    public boolean canPerformGapJump(Vec3d start, Vec3d target) {
        double distance = start.distanceTo(target);
        if (distance > 4.5) return false; // Maximum possible jump distance
        
        JumpTrajectory trajectory = simulateJump(start, target);
        return trajectory.isSuccess() && !trajectory.hasCollision();
    }
    
    /**
     * Check if wall climbing is possible
     */
    public boolean canClimbWall(Vec3d start, Vec3d target) {
        // Check if there's a wall to climb
        Vec3d direction = target.subtract(start).normalize();
        Vec3d wallCheck = start.add(direction.multiply(0.5));
        
        BlockPos wallPos = BlockPos.ofFloored(wallCheck);
        BlockState wallBlock = world.getBlockState(wallPos);
        
        if (wallBlock.isAir()) {
            return false; // No wall to climb
        }
        
        // Check if target position is clear
        return isPositionClear(target);
    }
    
    /**
     * Simulate falling from a position
     */
    public FallSimulation simulateFall(Vec3d start) {
        FallSimulation simulation = new FallSimulation(start);
        
        Vec3d position = start;
        Vec3d velocity = Vec3d.ZERO;
        
        for (int tick = 0; tick < MAX_SIMULATION_TICKS; tick++) {
            simulation.addPoint(position, velocity, tick);
            
            // Apply gravity
            velocity = velocity.subtract(0, GRAVITY, 0);
            
            // Update position
            Vec3d newPosition = position.add(velocity);
            
            // Check for ground collision
            if (checkGroundCollision(newPosition)) {
                simulation.setLandingPoint(newPosition);
                simulation.setLandingVelocity(velocity);
                simulation.calculateDamage();
                break;
            }
            
            position = newPosition;
        }
        
        return simulation;
    }
    
    /**
     * Check collision between two positions
     */
    public boolean checkCollision(Vec3d from, Vec3d to) {
        Box playerBox = createPlayerBoundingBox(to);
        
        // Check all blocks that the player box intersects
        int minX = (int) Math.floor(playerBox.minX);
        int minY = (int) Math.floor(playerBox.minY);
        int minZ = (int) Math.floor(playerBox.minZ);
        int maxX = (int) Math.ceil(playerBox.maxX);
        int maxY = (int) Math.ceil(playerBox.maxY);
        int maxZ = (int) Math.ceil(playerBox.maxZ);
        
        for (int x = minX; x <= maxX; x++) {
            for (int y = minY; y <= maxY; y++) {
                for (int z = minZ; z <= maxZ; z++) {
                    BlockPos pos = new BlockPos(x, y, z);
                    BlockState state = world.getBlockState(pos);
                    
                    if (!state.isAir()) {
                        Box blockBox = new Box(x, y, z, x + 1, y + 1, z + 1);
                        if (playerBox.intersects(blockBox)) {
                            return true;
                        }
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Check if position is clear for player
     */
    public boolean isPositionClear(Vec3d position) {
        return !checkCollision(position, position);
    }
    
    /**
     * Check ground collision
     */
    private boolean checkGroundCollision(Vec3d position) {
        Vec3d groundCheck = position.subtract(0, GROUND_TOLERANCE, 0);
        BlockPos groundPos = BlockPos.ofFloored(groundCheck);
        BlockState groundState = world.getBlockState(groundPos);
        
        return !groundState.isAir();
    }
    
    /**
     * Create player bounding box at position
     */
    private Box createPlayerBoundingBox(Vec3d position) {
        double halfWidth = PLAYER_WIDTH / 2;
        return new Box(
            position.x - halfWidth, position.y, position.z - halfWidth,
            position.x + halfWidth, position.y + PLAYER_HEIGHT, position.z + halfWidth
        );
    }
    
    /**
     * Check if position is near target
     */
    private boolean isNearTarget(Vec3d position, Vec3d target, double tolerance) {
        return position.distanceTo(target) <= tolerance;
    }
    
    /**
     * Calculate movement time between two positions
     */
    public double calculateMovementTime(Vec3d from, Vec3d to, boolean sprinting) {
        double distance = from.distanceTo(to);
        double speed = sprinting ? SPRINT_SPEED : WALK_SPEED;
        return distance / speed; // Time in ticks
    }
    
    /**
     * Validate if a movement is physically possible
     */
    public boolean validateMovement(Vec3d from, Vec3d to, double maxTime) {
        double distance = from.distanceTo(to);
        double maxDistance = SPRINT_SPEED * maxTime;
        
        return distance <= maxDistance && !checkCollision(from, to);
    }
}
