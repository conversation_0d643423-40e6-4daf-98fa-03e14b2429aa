package change.thisname.movement;

import net.minecraft.util.math.Vec3d;
import change.thisname.physics.PhysicsSimulator;

/**
 * Base class for all movement actions
 */
public abstract class MovementAction {
    protected final Vec3d startPosition;
    protected final Vec3d targetPosition;
    protected final ActionType actionType;
    protected final double duration;
    protected final double priority;
    
    // Execution state
    protected boolean started = false;
    protected boolean completed = false;
    protected boolean cancelled = false;
    protected double progress = 0.0;
    protected long startTime = 0;
    
    // Physics validation
    protected boolean physicsValidated = false;
    protected String validationError = null;
    
    public MovementAction(Vec3d startPosition, Vec3d targetPosition, ActionType actionType, double duration, double priority) {
        this.startPosition = startPosition;
        this.targetPosition = targetPosition;
        this.actionType = actionType;
        this.duration = duration;
        this.priority = priority;
    }
    
    /**
     * Validate the action using physics simulation
     */
    public abstract boolean validatePhysics(PhysicsSimulator physicsSimulator);
    
    /**
     * Execute the action for one tick
     */
    public abstract ActionResult executeTick(MovementController controller);
    
    /**
     * Get the current position during action execution
     */
    public abstract Vec3d getCurrentPosition();
    
    /**
     * Get required input states for this action
     */
    public abstract InputState getRequiredInputs();
    
    /**
     * Start executing the action
     */
    public void start() {
        if (!physicsValidated) {
            throw new IllegalStateException("Action must be physics validated before starting");
        }
        
        started = true;
        startTime = System.currentTimeMillis();
        progress = 0.0;
    }
    
    /**
     * Cancel the action
     */
    public void cancel() {
        cancelled = true;
        completed = true;
    }
    
    /**
     * Update progress based on elapsed time
     */
    protected void updateProgress() {
        if (!started || completed) {
            return;
        }
        
        long elapsed = System.currentTimeMillis() - startTime;
        progress = Math.min(1.0, elapsed / (duration * 1000.0));
        
        if (progress >= 1.0) {
            completed = true;
        }
    }
    
    /**
     * Interpolate position based on progress
     */
    protected Vec3d interpolatePosition() {
        return startPosition.lerp(targetPosition, progress);
    }
    
    /**
     * Check if action can be interrupted
     */
    public boolean canInterrupt() {
        return actionType != ActionType.PARKOUR_JUMP && actionType != ActionType.FALL;
    }
    
    /**
     * Get action priority for scheduling
     */
    public double getPriority() {
        return priority;
    }
    
    /**
     * Check if action requires precise timing
     */
    public boolean requiresPreciseTiming() {
        return actionType == ActionType.PARKOUR_JUMP || actionType == ActionType.PRECISION_JUMP;
    }
    
    // Getters
    public Vec3d getStartPosition() { return startPosition; }
    public Vec3d getTargetPosition() { return targetPosition; }
    public ActionType getActionType() { return actionType; }
    public double getDuration() { return duration; }
    public boolean isStarted() { return started; }
    public boolean isCompleted() { return completed; }
    public boolean isCancelled() { return cancelled; }
    public double getProgress() { return progress; }
    public boolean isPhysicsValidated() { return physicsValidated; }
    public String getValidationError() { return validationError; }
    
    protected void setPhysicsValidated(boolean validated) { this.physicsValidated = validated; }
    protected void setValidationError(String error) { this.validationError = error; }
    
    /**
     * Types of movement actions
     */
    public enum ActionType {
        WALK,
        SPRINT,
        JUMP,
        PRECISION_JUMP,
        PARKOUR_JUMP,
        CLIMB,
        FALL,
        SWIM,
        SNEAK
    }
    
    /**
     * Result of action execution
     */
    public static class ActionResult {
        private final boolean success;
        private final Vec3d newPosition;
        private final String errorMessage;
        private final boolean actionComplete;
        
        public ActionResult(boolean success, Vec3d newPosition, boolean actionComplete) {
            this(success, newPosition, actionComplete, null);
        }
        
        public ActionResult(boolean success, Vec3d newPosition, boolean actionComplete, String errorMessage) {
            this.success = success;
            this.newPosition = newPosition;
            this.actionComplete = actionComplete;
            this.errorMessage = errorMessage;
        }
        
        public boolean isSuccess() { return success; }
        public Vec3d getNewPosition() { return newPosition; }
        public boolean isActionComplete() { return actionComplete; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * Input states required for actions
     */
    public static class InputState {
        private boolean forward = false;
        private boolean backward = false;
        private boolean left = false;
        private boolean right = false;
        private boolean jump = false;
        private boolean sneak = false;
        private boolean sprint = false;
        
        public InputState() {}
        
        public InputState forward(boolean state) { this.forward = state; return this; }
        public InputState backward(boolean state) { this.backward = state; return this; }
        public InputState left(boolean state) { this.left = state; return this; }
        public InputState right(boolean state) { this.right = state; return this; }
        public InputState jump(boolean state) { this.jump = state; return this; }
        public InputState sneak(boolean state) { this.sneak = state; return this; }
        public InputState sprint(boolean state) { this.sprint = state; return this; }
        
        // Getters
        public boolean isForward() { return forward; }
        public boolean isBackward() { return backward; }
        public boolean isLeft() { return left; }
        public boolean isRight() { return right; }
        public boolean isJump() { return jump; }
        public boolean isSneak() { return sneak; }
        public boolean isSprint() { return sprint; }
    }
}
