package change.thisname.render;

import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.debug.DebugRenderer;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.math.Vec3d;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.render.RenderLayer;
import net.minecraft.client.render.VertexConsumer;
import net.minecraft.util.math.MathHelper;

import change.thisname.pathfinding.PathNode;
import change.thisname.pathfinding.PathfindingResult;
import change.thisname.physics.JumpTrajectory;
import change.thisname.movement.ParkourAction;

import java.util.List;
import java.util.ArrayList;

/**
 * Comprehensive rendering system for pathfinding visualization
 */
public class PathfindingRenderer {
    private final MinecraftClient client;
    
    // Rendering state
    private PathfindingResult currentResult;
    private List<PathNode> exploredNodes;
    private Vec3d goalPosition;
    private List<JumpTrajectory> jumpTrajectories;
    private List<Vec3d> obstaclePositions;
    
    // Rendering options
    private boolean renderPath = true;
    private boolean renderExploredNodes = true;
    private boolean renderGoalMarker = true;
    private boolean renderTrajectories = true;
    private boolean renderObstacles = true;
    
    // Colors (ARGB format)
    private static final int COLOR_PATH = 0xFF00FF00;           // Green
    private static final int COLOR_CURRENT_PATH = 0xFF0080FF;   // Blue
    private static final int COLOR_EXPLORED = 0x80808080;       // Gray (transparent)
    private static final int COLOR_GOAL = 0xFFFF0000;           // Red
    private static final int COLOR_TRAJECTORY = 0xFFFFFF00;     // Yellow
    private static final int COLOR_OBSTACLE = 0xFFFF8000;       // Orange
    private static final int COLOR_PARKOUR = 0xFFFF00FF;        // Magenta
    
    public PathfindingRenderer(MinecraftClient client) {
        this.client = client;
        this.exploredNodes = new ArrayList<>();
        this.jumpTrajectories = new ArrayList<>();
        this.obstaclePositions = new ArrayList<>();
    }
    
    /**
     * Render all pathfinding visualizations
     */
    public void render(MatrixStack matrices, VertexConsumerProvider vertexConsumers, double cameraX, double cameraY, double cameraZ) {
        if (client.player == null) {
            return;
        }
        
        // Render explored nodes
        if (renderExploredNodes && !exploredNodes.isEmpty()) {
            renderExploredNodes(matrices, vertexConsumers, cameraX, cameraY, cameraZ);
        }
        
        // Render final path
        if (renderPath && currentResult != null && currentResult.isSuccess()) {
            renderFinalPath(matrices, vertexConsumers, cameraX, cameraY, cameraZ);
        }
        
        // Render goal marker
        if (renderGoalMarker && goalPosition != null) {
            renderGoalMarker(matrices, vertexConsumers, cameraX, cameraY, cameraZ);
        }
        
        // Render jump trajectories
        if (renderTrajectories && !jumpTrajectories.isEmpty()) {
            renderJumpTrajectories(matrices, vertexConsumers, cameraX, cameraY, cameraZ);
        }
        
        // Render obstacles
        if (renderObstacles && !obstaclePositions.isEmpty()) {
            renderObstacles(matrices, vertexConsumers, cameraX, cameraY, cameraZ);
        }
    }
    
    /**
     * Render explored nodes as small cubes
     */
    private void renderExploredNodes(MatrixStack matrices, VertexConsumerProvider vertexConsumers, 
                                   double cameraX, double cameraY, double cameraZ) {
        VertexConsumer vertexConsumer = vertexConsumers.getBuffer(RenderLayer.getDebugLineStrip(1.0));
        
        for (PathNode node : exploredNodes) {
            Vec3d pos = node.getPrecisePosition();
            renderCube(matrices, vertexConsumer, pos, 0.1, COLOR_EXPLORED, cameraX, cameraY, cameraZ);
        }
    }
    
    /**
     * Render final calculated path
     */
    private void renderFinalPath(MatrixStack matrices, VertexConsumerProvider vertexConsumers,
                               double cameraX, double cameraY, double cameraZ) {
        List<PathNode> path = currentResult.getPath();
        if (path.size() < 2) {
            return;
        }
        
        VertexConsumer vertexConsumer = vertexConsumers.getBuffer(RenderLayer.getDebugLineStrip(3.0));
        
        for (int i = 0; i < path.size() - 1; i++) {
            PathNode current = path.get(i);
            PathNode next = path.get(i + 1);
            
            Vec3d currentPos = current.getPrecisePosition();
            Vec3d nextPos = next.getPrecisePosition();
            
            // Choose color based on movement type
            int color = getPathColor(next);
            
            renderLine(matrices, vertexConsumer, currentPos, nextPos, color, cameraX, cameraY, cameraZ);
            
            // Render node markers
            renderCube(matrices, vertexConsumer, currentPos, 0.15, color, cameraX, cameraY, cameraZ);
        }
        
        // Render final node
        PathNode lastNode = path.get(path.size() - 1);
        renderCube(matrices, vertexConsumer, lastNode.getPrecisePosition(), 0.15, COLOR_PATH, cameraX, cameraY, cameraZ);
    }
    
    /**
     * Get color for path segment based on movement type
     */
    private int getPathColor(PathNode node) {
        switch (node.getMovementType()) {
            case WALK:
                return COLOR_PATH;
            case SPRINT:
                return COLOR_CURRENT_PATH;
            case JUMP:
                return COLOR_TRAJECTORY;
            case PARKOUR_JUMP:
                return COLOR_PARKOUR;
            case CLIMB:
                return COLOR_OBSTACLE;
            case FALL:
                return 0xFFFF4444; // Light red
            default:
                return COLOR_PATH;
        }
    }
    
    /**
     * Render goal marker
     */
    private void renderGoalMarker(MatrixStack matrices, VertexConsumerProvider vertexConsumers,
                                double cameraX, double cameraY, double cameraZ) {
        VertexConsumer vertexConsumer = vertexConsumers.getBuffer(RenderLayer.getDebugLineStrip(2.0));
        
        // Render pulsing goal marker
        float time = (System.currentTimeMillis() % 2000) / 2000.0f;
        float scale = 0.3f + 0.2f * MathHelper.sin(time * (float) Math.PI * 2);
        
        renderCube(matrices, vertexConsumer, goalPosition, scale, COLOR_GOAL, cameraX, cameraY, cameraZ);
        
        // Render goal beacon (vertical line)
        Vec3d beaconTop = goalPosition.add(0, 5, 0);
        renderLine(matrices, vertexConsumer, goalPosition, beaconTop, COLOR_GOAL, cameraX, cameraY, cameraZ);
    }
    
    /**
     * Render jump trajectories
     */
    private void renderJumpTrajectories(MatrixStack matrices, VertexConsumerProvider vertexConsumers,
                                      double cameraX, double cameraY, double cameraZ) {
        VertexConsumer vertexConsumer = vertexConsumers.getBuffer(RenderLayer.getDebugLineStrip(1.5));
        
        for (JumpTrajectory trajectory : jumpTrajectories) {
            List<JumpTrajectory.TrajectoryPoint> points = trajectory.getTrajectoryPoints();
            
            for (int i = 0; i < points.size() - 1; i++) {
                Vec3d current = points.get(i).getPosition();
                Vec3d next = points.get(i + 1).getPosition();
                
                // Color based on trajectory success
                int color = trajectory.isSuccess() ? COLOR_TRAJECTORY : 0xFFFF4444;
                
                renderLine(matrices, vertexConsumer, current, next, color, cameraX, cameraY, cameraZ);
            }
            
            // Render landing point
            if (trajectory.getLandingPoint() != null) {
                renderCube(matrices, vertexConsumer, trajectory.getLandingPoint(), 0.2, 
                          trajectory.isSuccess() ? COLOR_TRAJECTORY : 0xFFFF4444, cameraX, cameraY, cameraZ);
            }
            
            // Render collision point if any
            if (trajectory.hasCollision() && trajectory.getCollisionPoint() != null) {
                renderCube(matrices, vertexConsumer, trajectory.getCollisionPoint(), 0.25, 
                          0xFFFF0000, cameraX, cameraY, cameraZ);
            }
        }
    }
    
    /**
     * Render obstacles
     */
    private void renderObstacles(MatrixStack matrices, VertexConsumerProvider vertexConsumers,
                               double cameraX, double cameraY, double cameraZ) {
        VertexConsumer vertexConsumer = vertexConsumers.getBuffer(RenderLayer.getDebugLineStrip(1.0));
        
        for (Vec3d obstacle : obstaclePositions) {
            renderCube(matrices, vertexConsumer, obstacle, 0.5, COLOR_OBSTACLE, cameraX, cameraY, cameraZ);
        }
    }
    
    /**
     * Render a line between two points
     */
    private void renderLine(MatrixStack matrices, VertexConsumer vertexConsumer, Vec3d start, Vec3d end, 
                          int color, double cameraX, double cameraY, double cameraZ) {
        matrices.push();
        
        float startX = (float) (start.x - cameraX);
        float startY = (float) (start.y - cameraY);
        float startZ = (float) (start.z - cameraZ);
        
        float endX = (float) (end.x - cameraX);
        float endY = (float) (end.y - cameraY);
        float endZ = (float) (end.z - cameraZ);
        
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;
        float a = ((color >> 24) & 0xFF) / 255.0f;
        
        vertexConsumer.vertex(matrices.peek().getPositionMatrix(), startX, startY, startZ)
                     .color(r, g, b, a).next();
        vertexConsumer.vertex(matrices.peek().getPositionMatrix(), endX, endY, endZ)
                     .color(r, g, b, a).next();
        
        matrices.pop();
    }
    
    /**
     * Render a cube at position
     */
    private void renderCube(MatrixStack matrices, VertexConsumer vertexConsumer, Vec3d position, 
                          double size, int color, double cameraX, double cameraY, double cameraZ) {
        matrices.push();
        
        float x = (float) (position.x - cameraX);
        float y = (float) (position.y - cameraY);
        float z = (float) (position.z - cameraZ);
        float s = (float) size;
        
        float r = ((color >> 16) & 0xFF) / 255.0f;
        float g = ((color >> 8) & 0xFF) / 255.0f;
        float b = (color & 0xFF) / 255.0f;
        float a = ((color >> 24) & 0xFF) / 255.0f;
        
        // Render cube edges
        Vec3d[] vertices = {
            new Vec3d(x - s, y - s, z - s), new Vec3d(x + s, y - s, z - s),
            new Vec3d(x + s, y + s, z - s), new Vec3d(x - s, y + s, z - s),
            new Vec3d(x - s, y - s, z + s), new Vec3d(x + s, y - s, z + s),
            new Vec3d(x + s, y + s, z + s), new Vec3d(x - s, y + s, z + s)
        };
        
        // Bottom face
        renderQuadOutline(matrices, vertexConsumer, vertices[0], vertices[1], vertices[2], vertices[3], r, g, b, a);
        // Top face
        renderQuadOutline(matrices, vertexConsumer, vertices[4], vertices[5], vertices[6], vertices[7], r, g, b, a);
        // Vertical edges
        renderLine(matrices, vertexConsumer, vertices[0], vertices[4], color, 0, 0, 0);
        renderLine(matrices, vertexConsumer, vertices[1], vertices[5], color, 0, 0, 0);
        renderLine(matrices, vertexConsumer, vertices[2], vertices[6], color, 0, 0, 0);
        renderLine(matrices, vertexConsumer, vertices[3], vertices[7], color, 0, 0, 0);
        
        matrices.pop();
    }
    
    /**
     * Render quad outline
     */
    private void renderQuadOutline(MatrixStack matrices, VertexConsumer vertexConsumer, 
                                 Vec3d v1, Vec3d v2, Vec3d v3, Vec3d v4, 
                                 float r, float g, float b, float a) {
        vertexConsumer.vertex(matrices.peek().getPositionMatrix(), (float) v1.x, (float) v1.y, (float) v1.z)
                     .color(r, g, b, a).next();
        vertexConsumer.vertex(matrices.peek().getPositionMatrix(), (float) v2.x, (float) v2.y, (float) v2.z)
                     .color(r, g, b, a).next();
        vertexConsumer.vertex(matrices.peek().getPositionMatrix(), (float) v3.x, (float) v3.y, (float) v3.z)
                     .color(r, g, b, a).next();
        vertexConsumer.vertex(matrices.peek().getPositionMatrix(), (float) v4.x, (float) v4.y, (float) v4.z)
                     .color(r, g, b, a).next();
    }
    
    // Update methods
    public void updatePathfindingResult(PathfindingResult result) {
        this.currentResult = result;
        if (result != null) {
            this.exploredNodes = result.getExploredNodes();
        }
    }
    
    public void updateGoalPosition(Vec3d goal) {
        this.goalPosition = goal;
    }
    
    public void addJumpTrajectory(JumpTrajectory trajectory) {
        this.jumpTrajectories.add(trajectory);
    }
    
    public void clearJumpTrajectories() {
        this.jumpTrajectories.clear();
    }
    
    public void addObstacle(Vec3d position) {
        this.obstaclePositions.add(position);
    }
    
    public void clearObstacles() {
        this.obstaclePositions.clear();
    }
    
    public void clearAll() {
        this.currentResult = null;
        this.exploredNodes.clear();
        this.goalPosition = null;
        this.jumpTrajectories.clear();
        this.obstaclePositions.clear();
    }
    
    // Rendering toggles
    public void setRenderPath(boolean render) { this.renderPath = render; }
    public void setRenderExploredNodes(boolean render) { this.renderExploredNodes = render; }
    public void setRenderGoalMarker(boolean render) { this.renderGoalMarker = render; }
    public void setRenderTrajectories(boolean render) { this.renderTrajectories = render; }
    public void setRenderObstacles(boolean render) { this.renderObstacles = render; }
    
    // Getters
    public boolean isRenderPath() { return renderPath; }
    public boolean isRenderExploredNodes() { return renderExploredNodes; }
    public boolean isRenderGoalMarker() { return renderGoalMarker; }
    public boolean isRenderTrajectories() { return renderTrajectories; }
    public boolean isRenderObstacles() { return renderObstacles; }
}
