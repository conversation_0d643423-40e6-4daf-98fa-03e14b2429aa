package change.thisname.pathfinding;

import net.minecraft.util.math.Vec3d;
import java.util.List;
import java.util.ArrayList;

/**
 * Result of a pathfinding operation containing the path and metadata
 */
public class PathfindingResult {
    private boolean success;
    private List<PathNode> path;
    private List<PathNode> exploredNodes;
    private int iterations;
    private long computationTime;
    private Vec3d startPosition;
    private Vec3d goalPosition;
    private String failureReason;
    
    // Path statistics
    private double totalDistance;
    private double estimatedTravelTime;
    private int jumpCount;
    private int parkourMoveCount;
    private boolean containsDangerousMoves;
    
    public PathfindingResult() {
        this.success = false;
        this.path = new ArrayList<>();
        this.exploredNodes = new ArrayList<>();
        this.iterations = 0;
        this.computationTime = 0;
        this.failureReason = "";
        this.totalDistance = 0;
        this.estimatedTravelTime = 0;
        this.jumpCount = 0;
        this.parkourMoveCount = 0;
        this.containsDangerousMoves = false;
    }
    
    /**
     * Calculate path statistics after path is set
     */
    public void calculateStatistics() {
        if (path == null || path.isEmpty()) {
            return;
        }
        
        totalDistance = 0;
        estimatedTravelTime = 0;
        jumpCount = 0;
        parkourMoveCount = 0;
        containsDangerousMoves = false;
        
        for (int i = 1; i < path.size(); i++) {
            PathNode current = path.get(i);
            PathNode previous = path.get(i - 1);
            
            // Calculate distance
            double segmentDistance = current.getPrecisePosition().distanceTo(previous.getPrecisePosition());
            totalDistance += segmentDistance;
            
            // Calculate time based on movement type
            double segmentTime = calculateSegmentTime(current, segmentDistance);
            estimatedTravelTime += segmentTime;
            
            // Count special moves
            if (current.isJumpNode()) {
                jumpCount++;
            }
            
            if (current.isParkourNode()) {
                parkourMoveCount++;
                containsDangerousMoves = true;
            }
            
            // Check for dangerous moves
            if (current.getMovementType() == PathNode.MovementType.FALL && 
                segmentDistance > 5.0) {
                containsDangerousMoves = true;
            }
        }
    }
    
    /**
     * Calculate time for a path segment based on movement type
     */
    private double calculateSegmentTime(PathNode node, double distance) {
        double baseSpeed = 4.317; // Minecraft walking speed in blocks/second
        
        switch (node.getMovementType()) {
            case WALK:
                return distance / baseSpeed;
            case SPRINT:
                return distance / (baseSpeed * 1.3); // Sprinting is 30% faster
            case JUMP:
                return distance / baseSpeed + 0.5; // Add jump time
            case PARKOUR_JUMP:
                return distance / baseSpeed + 1.0; // Add parkour setup time
            case CLIMB:
                return distance / (baseSpeed * 0.3); // Climbing is much slower
            case FALL:
                return distance / (baseSpeed * 2.0); // Falling is faster
            default:
                return distance / baseSpeed;
        }
    }
    
    /**
     * Get a simplified path with only essential waypoints
     */
    public List<Vec3d> getSimplifiedPath() {
        List<Vec3d> simplified = new ArrayList<>();
        
        if (path == null || path.isEmpty()) {
            return simplified;
        }
        
        // Always include start
        simplified.add(path.get(0).getPrecisePosition());
        
        // Include nodes where movement type changes or direction changes significantly
        for (int i = 1; i < path.size() - 1; i++) {
            PathNode current = path.get(i);
            PathNode previous = path.get(i - 1);
            PathNode next = path.get(i + 1);
            
            // Include if movement type changes
            if (current.getMovementType() != previous.getMovementType()) {
                simplified.add(current.getPrecisePosition());
                continue;
            }
            
            // Include if it's a jump or parkour node
            if (current.isJumpNode() || current.isParkourNode()) {
                simplified.add(current.getPrecisePosition());
                continue;
            }
            
            // Include if direction changes significantly
            Vec3d dir1 = current.getPrecisePosition().subtract(previous.getPrecisePosition()).normalize();
            Vec3d dir2 = next.getPrecisePosition().subtract(current.getPrecisePosition()).normalize();
            double dot = dir1.dotProduct(dir2);
            
            if (dot < 0.8) { // Significant direction change
                simplified.add(current.getPrecisePosition());
            }
        }
        
        // Always include goal
        if (path.size() > 1) {
            simplified.add(path.get(path.size() - 1).getPrecisePosition());
        }
        
        return simplified;
    }
    
    // Getters and setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public List<PathNode> getPath() { return path; }
    public void setPath(List<PathNode> path) { 
        this.path = path; 
        calculateStatistics();
    }
    
    public List<PathNode> getExploredNodes() { return exploredNodes; }
    public void setExploredNodes(List<PathNode> exploredNodes) { this.exploredNodes = exploredNodes; }
    
    public int getIterations() { return iterations; }
    public void setIterations(int iterations) { this.iterations = iterations; }
    
    public long getComputationTime() { return computationTime; }
    public void setComputationTime(long computationTime) { this.computationTime = computationTime; }
    
    public Vec3d getStartPosition() { return startPosition; }
    public void setStartPosition(Vec3d startPosition) { this.startPosition = startPosition; }
    
    public Vec3d getGoalPosition() { return goalPosition; }
    public void setGoalPosition(Vec3d goalPosition) { this.goalPosition = goalPosition; }
    
    public String getFailureReason() { return failureReason; }
    public void setFailureReason(String failureReason) { this.failureReason = failureReason; }
    
    public double getTotalDistance() { return totalDistance; }
    public double getEstimatedTravelTime() { return estimatedTravelTime; }
    public int getJumpCount() { return jumpCount; }
    public int getParkourMoveCount() { return parkourMoveCount; }
    public boolean containsDangerousMoves() { return containsDangerousMoves; }
}
