package change.thisname.movement;

import net.minecraft.util.math.Vec3d;
import change.thisname.physics.PhysicsSimulator;
import change.thisname.physics.JumpTrajectory;

/**
 * Advanced parkour movement action with precise physics simulation
 */
public class ParkourAction extends MovementAction {
    private final ParkourType parkourType;
    private final double requiredVelocity;
    private final Vec3d jumpDirection;
    private final boolean requiresPrecision;
    
    private JumpTrajectory trajectory;
    private Vec3d currentPosition;
    private Vec3d currentVelocity;
    private int currentTick;
    private int preparationTicks;
    private boolean inPreparation;
    
    public ParkourAction(Vec3d startPosition, Vec3d targetPosition, ParkourType parkourType, 
                        double requiredVelocity, Vec3d jumpDirection, boolean requiresPrecision) {
        super(startPosition, targetPosition, ActionType.PARKOUR_JUMP, 
              calculateParkourDuration(startPosition, targetPosition, parkourType), 3.0);
        this.parkourType = parkourType;
        this.requiredVelocity = requiredVelocity;
        this.jumpDirection = jumpDirection;
        this.requiresPrecision = requiresPrecision;
        this.currentPosition = startPosition;
        this.currentVelocity = Vec3d.ZERO;
        this.currentTick = 0;
        this.preparationTicks = calculatePreparationTicks(parkourType);
        this.inPreparation = true;
    }
    
    private static double calculateParkourDuration(Vec3d start, Vec3d target, ParkourType type) {
        double baseTime = start.distanceTo(target) / PhysicsSimulator.SPRINT_SPEED / 20.0;
        
        switch (type) {
            case GAP_JUMP:
                return baseTime + 1.0; // Extra time for gap jumps
            case PRECISION_JUMP:
                return baseTime + 0.5; // Slight extra time for precision
            case WALL_CLIMB:
                return baseTime * 2.0; // Wall climbing is slower
            case MOMENTUM_JUMP:
                return baseTime + 1.5; // Requires momentum buildup
            default:
                return baseTime;
        }
    }
    
    private static int calculatePreparationTicks(ParkourType type) {
        switch (type) {
            case GAP_JUMP:
                return 10; // Need momentum for gap jumps
            case PRECISION_JUMP:
                return 5; // Brief preparation for precision
            case WALL_CLIMB:
                return 15; // Positioning for wall climb
            case MOMENTUM_JUMP:
                return 20; // Maximum momentum buildup
            default:
                return 5;
        }
    }
    
    @Override
    public boolean validatePhysics(PhysicsSimulator physicsSimulator) {
        // Validate based on parkour type
        switch (parkourType) {
            case GAP_JUMP:
                return validateGapJump(physicsSimulator);
            case PRECISION_JUMP:
                return validatePrecisionJump(physicsSimulator);
            case WALL_CLIMB:
                return validateWallClimb(physicsSimulator);
            case MOMENTUM_JUMP:
                return validateMomentumJump(physicsSimulator);
            default:
                setValidationError("Unknown parkour type");
                return false;
        }
    }
    
    private boolean validateGapJump(PhysicsSimulator physicsSimulator) {
        double distance = startPosition.distanceTo(targetPosition);
        
        // Check maximum gap jump distance
        if (distance > 4.5) {
            setValidationError("Gap too wide: " + distance + " blocks");
            return false;
        }
        
        // Simulate jump with required velocity
        trajectory = physicsSimulator.simulateJump(startPosition, targetPosition, requiredVelocity);
        
        if (!trajectory.isSuccess()) {
            setValidationError("Gap jump trajectory failed");
            return false;
        }
        
        if (trajectory.hasCollision()) {
            setValidationError("Gap jump blocked by obstacle");
            return false;
        }
        
        // Check landing safety
        Vec3d landingPoint = trajectory.getLandingPoint();
        if (landingPoint != null && !physicsSimulator.isPositionClear(landingPoint)) {
            setValidationError("Unsafe landing point");
            return false;
        }
        
        setPhysicsValidated(true);
        return true;
    }
    
    private boolean validatePrecisionJump(PhysicsSimulator physicsSimulator) {
        // Precision jumps require very accurate landing
        trajectory = physicsSimulator.simulateJump(startPosition, targetPosition, requiredVelocity);
        
        if (!trajectory.isSuccess()) {
            setValidationError("Precision jump trajectory failed");
            return false;
        }
        
        // Check accuracy - must land within 0.5 blocks of target
        Vec3d landingPoint = trajectory.getLandingPoint();
        if (landingPoint == null || landingPoint.distanceTo(targetPosition) > 0.5) {
            setValidationError("Precision jump not accurate enough");
            return false;
        }
        
        setPhysicsValidated(true);
        return true;
    }
    
    private boolean validateWallClimb(PhysicsSimulator physicsSimulator) {
        // Check if there's actually a wall to climb
        if (!physicsSimulator.canClimbWall(startPosition, targetPosition)) {
            setValidationError("No climbable wall found");
            return false;
        }
        
        // Check height difference
        double heightDiff = targetPosition.y - startPosition.y;
        if (heightDiff > 3.0) {
            setValidationError("Wall too high to climb: " + heightDiff + " blocks");
            return false;
        }
        
        setPhysicsValidated(true);
        return true;
    }
    
    private boolean validateMomentumJump(PhysicsSimulator physicsSimulator) {
        // Momentum jumps require significant horizontal distance
        double horizontalDistance = Math.sqrt(
            Math.pow(targetPosition.x - startPosition.x, 2) + 
            Math.pow(targetPosition.z - startPosition.z, 2)
        );
        
        if (horizontalDistance < 3.0) {
            setValidationError("Insufficient distance for momentum jump");
            return false;
        }
        
        // Simulate with maximum velocity
        trajectory = physicsSimulator.simulateJump(startPosition, targetPosition, requiredVelocity);
        
        if (!trajectory.isSuccess()) {
            setValidationError("Momentum jump trajectory failed");
            return false;
        }
        
        setPhysicsValidated(true);
        return true;
    }
    
    @Override
    public ActionResult executeTick(IMovementController controller) {
        if (!started) {
            start();
        }
        
        updateProgress();
        
        if (inPreparation) {
            // Preparation phase - build momentum or position
            return executePreparation(controller);
        } else {
            // Execution phase - perform the parkour move
            return executeJump(controller);
        }
    }
    
    private ActionResult executePreparation(IMovementController controller) {
        if (currentTick >= preparationTicks) {
            inPreparation = false;
            currentTick = 0;
            return new ActionResult(true, currentPosition, false);
        }
        
        InputState inputs = getPreparationInputs();
        controller.applyInputs(inputs);
        
        // Update position during preparation (slight movement for momentum)
        if (parkourType == ParkourType.MOMENTUM_JUMP || parkourType == ParkourType.GAP_JUMP) {
            Vec3d direction = jumpDirection.normalize();
            currentPosition = currentPosition.add(direction.multiply(0.1));
        }
        
        currentTick++;
        return new ActionResult(true, currentPosition, false);
    }
    
    private ActionResult executeJump(IMovementController controller) {
        // Get position from trajectory
        if (trajectory != null && currentTick < trajectory.getTrajectoryPoints().size()) {
            JumpTrajectory.TrajectoryPoint point = trajectory.getTrajectoryPoints().get(currentTick);
            currentPosition = point.getPosition();
            currentVelocity = point.getVelocity();
        } else {
            // Fallback to interpolation
            currentPosition = interpolatePosition();
        }
        
        InputState inputs = getRequiredInputs();
        controller.applyInputs(inputs);
        
        currentTick++;
        
        return new ActionResult(true, currentPosition, completed);
    }
    
    private InputState getPreparationInputs() {
        InputState inputs = new InputState();
        
        switch (parkourType) {
            case GAP_JUMP:
            case MOMENTUM_JUMP:
                // Build forward momentum
                inputs.forward(true).sprint(true);
                break;
            case PRECISION_JUMP:
                // Careful positioning
                Vec3d direction = targetPosition.subtract(startPosition).normalize();
                if (Math.abs(direction.z) > Math.abs(direction.x)) {
                    inputs.forward(direction.z > 0);
                    inputs.backward(direction.z < 0);
                } else {
                    inputs.right(direction.x > 0);
                    inputs.left(direction.x < 0);
                }
                break;
            case WALL_CLIMB:
                // Position near wall
                inputs.forward(true);
                break;
        }
        
        return inputs;
    }
    
    @Override
    public Vec3d getCurrentPosition() {
        return currentPosition;
    }
    
    @Override
    public InputState getRequiredInputs() {
        if (completed) {
            return new InputState();
        }
        
        InputState inputs = new InputState();
        
        // Jump input at the right moment
        if (currentTick <= 2 && !inPreparation) {
            inputs.jump(true);
        }
        
        // Directional inputs based on jump direction
        if (Math.abs(jumpDirection.z) > Math.abs(jumpDirection.x)) {
            if (jumpDirection.z > 0) {
                inputs.forward(true);
            } else {
                inputs.backward(true);
            }
        } else {
            if (jumpDirection.x > 0) {
                inputs.right(true);
            } else {
                inputs.left(true);
            }
        }
        
        // Sprint for momentum-based jumps
        if (parkourType == ParkourType.MOMENTUM_JUMP || parkourType == ParkourType.GAP_JUMP) {
            inputs.sprint(true);
        }
        
        return inputs;
    }
    
    @Override
    public boolean canInterrupt() {
        return inPreparation; // Can only interrupt during preparation
    }
    
    // Getters
    public ParkourType getParkourType() { return parkourType; }
    public double getRequiredVelocity() { return requiredVelocity; }
    public Vec3d getJumpDirection() { return jumpDirection; }
    public boolean requiresPrecision() { return requiresPrecision; }
    public JumpTrajectory getTrajectory() { return trajectory; }
    public boolean isInPreparation() { return inPreparation; }
    
    /**
     * Types of parkour movements
     */
    public enum ParkourType {
        GAP_JUMP,       // Jump across gaps
        PRECISION_JUMP, // Precise landing jumps
        WALL_CLIMB,     // Climb up walls
        MOMENTUM_JUMP   // Long distance jumps with momentum
    }
}
