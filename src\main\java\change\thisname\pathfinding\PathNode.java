package change.thisname.pathfinding;

import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

/**
 * Represents a node in the A* pathfinding algorithm.
 * Contains position, costs, and parent reference for path reconstruction.
 */
public class PathNode implements Comparable<PathNode> {
    private final BlockPos position;
    private final Vec3d precisePosition;
    private PathNode parent;
    
    // A* algorithm costs
    private double gCost; // Actual cost from start to this node
    private double hCost; // Heuristic cost from this node to goal
    private double fCost; // Total cost (g + h)
    
    // Movement metadata
    private MovementType movementType;
    private boolean isJumpNode;
    private boolean isParkourNode;
    private double jumpVelocity;
    private Vec3d jumpDirection;
    
    public PathNode(BlockPos position) {
        this.position = position;
        this.precisePosition = Vec3d.ofCenter(position);
        this.gCost = 0;
        this.hCost = 0;
        this.fCost = 0;
        this.movementType = MovementType.WALK;
        this.isJumpNode = false;
        this.isParkourNode = false;
        this.jumpVelocity = 0;
        this.jumpDirection = Vec3d.ZERO;
    }
    
    public PathNode(Vec3d precisePosition) {
        this.position = BlockPos.ofFloored(precisePosition);
        this.precisePosition = precisePosition;
        this.gCost = 0;
        this.hCost = 0;
        this.fCost = 0;
        this.movementType = MovementType.WALK;
        this.isJumpNode = false;
        this.isParkourNode = false;
        this.jumpVelocity = 0;
        this.jumpDirection = Vec3d.ZERO;
    }
    
    /**
     * Calculate G cost (actual cost from start to this node)
     */
    public void calculateGCost(PathNode startNode) {
        if (parent == null) {
            gCost = 0;
            return;
        }
        
        double baseCost = parent.getGCost();
        double movementCost = calculateMovementCost(parent);
        
        gCost = baseCost + movementCost;
        updateFCost();
    }
    
    /**
     * Calculate H cost (heuristic cost from this node to goal)
     */
    public void calculateHCost(PathNode goalNode) {
        // Use 3D Euclidean distance as heuristic
        Vec3d goalPos = goalNode.getPrecisePosition();
        double dx = precisePosition.x - goalPos.x;
        double dy = precisePosition.y - goalPos.y;
        double dz = precisePosition.z - goalPos.z;
        
        hCost = Math.sqrt(dx * dx + dy * dy + dz * dz);
        updateFCost();
    }
    
    /**
     * Calculate movement cost from parent to this node
     */
    private double calculateMovementCost(PathNode parent) {
        double distance = parent.getPrecisePosition().distanceTo(this.precisePosition);
        double baseCost = distance;
        
        // Apply movement type multipliers
        switch (movementType) {
            case WALK:
                baseCost *= 1.0;
                break;
            case SPRINT:
                baseCost *= 0.8; // Sprinting is faster
                break;
            case JUMP:
                baseCost *= 1.5; // Jumping costs more energy
                break;
            case PARKOUR_JUMP:
                baseCost *= 2.0; // Parkour jumps are risky and costly
                break;
            case CLIMB:
                baseCost *= 3.0; // Climbing is slow and costly
                break;
            case FALL:
                baseCost *= 0.5; // Falling is fast but risky
                break;
        }
        
        // Add height penalty for vertical movement
        double heightDiff = Math.abs(precisePosition.y - parent.getPrecisePosition().y);
        if (heightDiff > 0) {
            baseCost += heightDiff * 0.5;
        }
        
        // Add parkour penalty
        if (isParkourNode) {
            baseCost *= 1.3;
        }
        
        return baseCost;
    }
    
    private void updateFCost() {
        fCost = gCost + hCost;
    }
    
    @Override
    public int compareTo(PathNode other) {
        int fCompare = Double.compare(this.fCost, other.fCost);
        if (fCompare != 0) {
            return fCompare;
        }
        // If F costs are equal, prefer lower H cost (closer to goal)
        return Double.compare(this.hCost, other.hCost);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PathNode pathNode = (PathNode) obj;
        return position.equals(pathNode.position);
    }
    
    @Override
    public int hashCode() {
        return position.hashCode();
    }
    
    // Getters and setters
    public BlockPos getPosition() { return position; }
    public Vec3d getPrecisePosition() { return precisePosition; }
    public PathNode getParent() { return parent; }
    public void setParent(PathNode parent) { this.parent = parent; }
    public double getGCost() { return gCost; }
    public void setGCost(double gCost) { this.gCost = gCost; updateFCost(); }
    public double getHCost() { return hCost; }
    public void setHCost(double hCost) { this.hCost = hCost; updateFCost(); }
    public double getFCost() { return fCost; }
    public MovementType getMovementType() { return movementType; }
    public void setMovementType(MovementType movementType) { this.movementType = movementType; }
    public boolean isJumpNode() { return isJumpNode; }
    public void setJumpNode(boolean jumpNode) { this.isJumpNode = jumpNode; }
    public boolean isParkourNode() { return isParkourNode; }
    public void setParkourNode(boolean parkourNode) { this.isParkourNode = parkourNode; }
    public double getJumpVelocity() { return jumpVelocity; }
    public void setJumpVelocity(double jumpVelocity) { this.jumpVelocity = jumpVelocity; }
    public Vec3d getJumpDirection() { return jumpDirection; }
    public void setJumpDirection(Vec3d jumpDirection) { this.jumpDirection = jumpDirection; }
    
    /**
     * Movement types for different actions
     */
    public enum MovementType {
        WALK,
        SPRINT,
        JUMP,
        PARKOUR_JUMP,
        CLIMB,
        FALL
    }
}
