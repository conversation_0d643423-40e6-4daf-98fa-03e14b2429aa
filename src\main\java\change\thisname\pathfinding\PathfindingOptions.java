package change.thisname.pathfinding;

/**
 * Configuration options for pathfinding behavior
 */
public class PathfindingOptions {
    private boolean allowJumping = true;
    private boolean allowParkour = true;
    private boolean allowSprinting = true;
    private boolean allowFalling = true;
    private boolean allowClimbing = false;
    
    private double goalTolerance = 1.0;
    private double maxSearchDistance = 100.0;
    private int maxIterations = 10000;
    
    private boolean preferSaferPaths = false;
    private boolean preferFasterPaths = true;
    private boolean avoidDamage = true;
    
    // Movement constraints
    private double maxJumpDistance = 4.0;
    private double maxFallDistance = 10.0;
    private double maxClimbHeight = 5.0;
    
    public PathfindingOptions() {
        // Default constructor with default values
    }
    
    // Builder pattern methods
    public PathfindingOptions allowJumping(boolean allow) {
        this.allowJumping = allow;
        return this;
    }
    
    public PathfindingOptions allowParkour(boolean allow) {
        this.allowParkour = allow;
        return this;
    }
    
    public PathfindingOptions allowSprinting(boolean allow) {
        this.allowSprinting = allow;
        return this;
    }
    
    public PathfindingOptions allowFalling(boolean allow) {
        this.allowFalling = allow;
        return this;
    }
    
    public PathfindingOptions allowClimbing(boolean allow) {
        this.allowClimbing = allow;
        return this;
    }
    
    public PathfindingOptions goalTolerance(double tolerance) {
        this.goalTolerance = tolerance;
        return this;
    }
    
    public PathfindingOptions maxSearchDistance(double distance) {
        this.maxSearchDistance = distance;
        return this;
    }
    
    public PathfindingOptions maxIterations(int iterations) {
        this.maxIterations = iterations;
        return this;
    }
    
    public PathfindingOptions preferSaferPaths(boolean prefer) {
        this.preferSaferPaths = prefer;
        return this;
    }
    
    public PathfindingOptions preferFasterPaths(boolean prefer) {
        this.preferFasterPaths = prefer;
        return this;
    }
    
    public PathfindingOptions avoidDamage(boolean avoid) {
        this.avoidDamage = avoid;
        return this;
    }
    
    public PathfindingOptions maxJumpDistance(double distance) {
        this.maxJumpDistance = distance;
        return this;
    }
    
    public PathfindingOptions maxFallDistance(double distance) {
        this.maxFallDistance = distance;
        return this;
    }
    
    public PathfindingOptions maxClimbHeight(double height) {
        this.maxClimbHeight = height;
        return this;
    }
    
    // Getters
    public boolean isAllowJumping() { return allowJumping; }
    public boolean isAllowParkour() { return allowParkour; }
    public boolean isAllowSprinting() { return allowSprinting; }
    public boolean isAllowFalling() { return allowFalling; }
    public boolean isAllowClimbing() { return allowClimbing; }
    public double getGoalTolerance() { return goalTolerance; }
    public double getMaxSearchDistance() { return maxSearchDistance; }
    public int getMaxIterations() { return maxIterations; }
    public boolean isPreferSaferPaths() { return preferSaferPaths; }
    public boolean isPreferFasterPaths() { return preferFasterPaths; }
    public boolean isAvoidDamage() { return avoidDamage; }
    public double getMaxJumpDistance() { return maxJumpDistance; }
    public double getMaxFallDistance() { return maxFallDistance; }
    public double getMaxClimbHeight() { return maxClimbHeight; }
    
    // Setters
    public void setAllowJumping(boolean allowJumping) { this.allowJumping = allowJumping; }
    public void setAllowParkour(boolean allowParkour) { this.allowParkour = allowParkour; }
    public void setAllowSprinting(boolean allowSprinting) { this.allowSprinting = allowSprinting; }
    public void setAllowFalling(boolean allowFalling) { this.allowFalling = allowFalling; }
    public void setAllowClimbing(boolean allowClimbing) { this.allowClimbing = allowClimbing; }
    public void setGoalTolerance(double goalTolerance) { this.goalTolerance = goalTolerance; }
    public void setMaxSearchDistance(double maxSearchDistance) { this.maxSearchDistance = maxSearchDistance; }
    public void setMaxIterations(int maxIterations) { this.maxIterations = maxIterations; }
    public void setPreferSaferPaths(boolean preferSaferPaths) { this.preferSaferPaths = preferSaferPaths; }
    public void setPreferFasterPaths(boolean preferFasterPaths) { this.preferFasterPaths = preferFasterPaths; }
    public void setAvoidDamage(boolean avoidDamage) { this.avoidDamage = avoidDamage; }
    public void setMaxJumpDistance(double maxJumpDistance) { this.maxJumpDistance = maxJumpDistance; }
    public void setMaxFallDistance(double maxFallDistance) { this.maxFallDistance = maxFallDistance; }
    public void setMaxClimbHeight(double maxClimbHeight) { this.maxClimbHeight = maxClimbHeight; }
}
