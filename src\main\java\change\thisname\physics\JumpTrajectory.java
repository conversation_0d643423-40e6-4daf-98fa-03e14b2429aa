package change.thisname.physics;

import net.minecraft.util.math.Vec3d;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a jump trajectory with physics simulation data
 */
public class JumpTrajectory {
    private final Vec3d startPosition;
    private final Vec3d targetPosition;
    private final List<TrajectoryPoint> trajectoryPoints;
    
    private boolean success;
    private boolean hasCollision;
    private Vec3d landingPoint;
    private Vec3d collisionPoint;
    private double maxHeight;
    private double totalTime;
    private double horizontalDistance;
    private double accuracy;
    
    public JumpTrajectory(Vec3d start, Vec3d target) {
        this.startPosition = start;
        this.targetPosition = target;
        this.trajectoryPoints = new ArrayList<>();
        this.success = false;
        this.hasCollision = false;
        this.maxHeight = start.y;
        this.totalTime = 0;
        this.horizontalDistance = 0;
        this.accuracy = 0;
    }
    
    /**
     * Add a point to the trajectory
     */
    public void addPoint(Vec3d position, Vec3d velocity, int tick) {
        TrajectoryPoint point = new TrajectoryPoint(position, velocity, tick);
        trajectoryPoints.add(point);
        
        // Update max height
        if (position.y > maxHeight) {
            maxHeight = position.y;
        }
        
        totalTime = tick;
    }
    
    /**
     * Calculate trajectory metrics
     */
    public void calculateMetrics() {
        if (trajectoryPoints.isEmpty()) {
            return;
        }
        
        Vec3d finalPosition = trajectoryPoints.get(trajectoryPoints.size() - 1).getPosition();
        
        // Calculate horizontal distance traveled
        Vec3d startHorizontal = new Vec3d(startPosition.x, 0, startPosition.z);
        Vec3d endHorizontal = new Vec3d(finalPosition.x, 0, finalPosition.z);
        horizontalDistance = startHorizontal.distanceTo(endHorizontal);
        
        // Calculate accuracy (how close we got to target)
        if (landingPoint != null) {
            accuracy = 1.0 / (1.0 + landingPoint.distanceTo(targetPosition));
        } else {
            accuracy = 0;
        }
    }
    
    /**
     * Get trajectory point at specific time
     */
    public TrajectoryPoint getPointAtTime(int tick) {
        for (TrajectoryPoint point : trajectoryPoints) {
            if (point.getTick() == tick) {
                return point;
            }
        }
        return null;
    }
    
    /**
     * Get trajectory points within time range
     */
    public List<TrajectoryPoint> getPointsInRange(int startTick, int endTick) {
        List<TrajectoryPoint> rangePoints = new ArrayList<>();
        for (TrajectoryPoint point : trajectoryPoints) {
            if (point.getTick() >= startTick && point.getTick() <= endTick) {
                rangePoints.add(point);
            }
        }
        return rangePoints;
    }
    
    /**
     * Check if trajectory passes through a specific area
     */
    public boolean passesThrough(Vec3d center, double radius) {
        for (TrajectoryPoint point : trajectoryPoints) {
            if (point.getPosition().distanceTo(center) <= radius) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get the highest point in the trajectory
     */
    public TrajectoryPoint getHighestPoint() {
        TrajectoryPoint highest = null;
        double maxY = Double.NEGATIVE_INFINITY;
        
        for (TrajectoryPoint point : trajectoryPoints) {
            if (point.getPosition().y > maxY) {
                maxY = point.getPosition().y;
                highest = point;
            }
        }
        
        return highest;
    }
    
    /**
     * Get simplified trajectory for rendering (every nth point)
     */
    public List<Vec3d> getSimplifiedTrajectory(int step) {
        List<Vec3d> simplified = new ArrayList<>();
        for (int i = 0; i < trajectoryPoints.size(); i += step) {
            simplified.add(trajectoryPoints.get(i).getPosition());
        }
        return simplified;
    }
    
    // Getters and setters
    public Vec3d getStartPosition() { return startPosition; }
    public Vec3d getTargetPosition() { return targetPosition; }
    public List<TrajectoryPoint> getTrajectoryPoints() { return trajectoryPoints; }
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    public boolean hasCollision() { return hasCollision; }
    public void setHasCollision(boolean hasCollision) { this.hasCollision = hasCollision; }
    public Vec3d getLandingPoint() { return landingPoint; }
    public void setLandingPoint(Vec3d landingPoint) { this.landingPoint = landingPoint; }
    public Vec3d getCollisionPoint() { return collisionPoint; }
    public void setCollisionPoint(Vec3d collisionPoint) { 
        this.collisionPoint = collisionPoint; 
        this.hasCollision = true;
    }
    public double getMaxHeight() { return maxHeight; }
    public double getTotalTime() { return totalTime; }
    public double getHorizontalDistance() { return horizontalDistance; }
    public double getAccuracy() { return accuracy; }
    
    /**
     * Represents a single point in the trajectory
     */
    public static class TrajectoryPoint {
        private final Vec3d position;
        private final Vec3d velocity;
        private final int tick;
        
        public TrajectoryPoint(Vec3d position, Vec3d velocity, int tick) {
            this.position = position;
            this.velocity = velocity;
            this.tick = tick;
        }
        
        public Vec3d getPosition() { return position; }
        public Vec3d getVelocity() { return velocity; }
        public int getTick() { return tick; }
        public double getSpeed() { return velocity.length(); }
        public double getTime() { return tick / 20.0; } // Convert ticks to seconds
    }
}
